'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { formatDate, formatDateTime } from '@/lib/utils'
import { tasksApi, projectsApi } from '@/lib/api'
import {
  Calendar,
  Clock,
  User,
  MapPin,
  Edit,
  CheckCircle,
  AlertCircle,
  XCircle,
  Building,
  FileText,
  MoreVertical,
  X,
  RotateCcw,
  Trash2,
  ExternalLink,
  BarChart3
} from 'lucide-react'
import type { Shoot, Task } from '@/types'

interface ShootCardProps {
  shoot: Shoot
  onEdit?: (shoot: Shoot) => void
  onStatusChange?: (shoot: Shoot, status: string) => void
  onCancel?: (shoot: Shoot) => void
  onDelete?: (shoot: Shoot) => void
  compact?: boolean
}

export function ShootCard({ shoot, onEdit, onStatusChange, onCancel, onDelete, compact = false }: ShootCardProps) {
  const router = useRouter()
  const [isDeleting, setIsDeleting] = useState(false)
  const [tasks, setTasks] = useState<Task[]>([])
  const [tasksLoading, setTasksLoading] = useState(false)

  // Fetch tasks for this shoot
  useEffect(() => {
    const fetchTasks = async () => {
      try {
        setTasksLoading(true)
        const allTasks = await tasksApi.getAll()
        // Include both shoot-specific tasks and project-level tasks (default tasks)
        const shootTasks = allTasks.filter(task =>
          task.shoot_id === shoot.id ||
          (task.project_id === shoot.project_id && !task.shoot_id)
        )

        // If no tasks exist for this project and we have client type, create default tasks
        if (shootTasks.length === 0 && shoot.project?.client?.client_type) {
          try {
            await projectsApi.createDefaultTasks(
              shoot.project_id,
              shoot.project.client.client_type,
              shoot.scheduled_date
            )

            // Refetch tasks after creating default ones
            const updatedTasks = await tasksApi.getAll()
            const updatedShootTasks = updatedTasks.filter(task =>
              task.shoot_id === shoot.id ||
              (task.project_id === shoot.project_id && !task.shoot_id)
            )
            setTasks(updatedShootTasks)
          } catch (taskError) {
            console.error('Failed to create default tasks:', taskError)
            setTasks(shootTasks) // Set empty tasks if creation fails
          }
        } else {
          setTasks(shootTasks)
        }
      } catch (error) {
        console.error('Error fetching tasks for shoot:', error)
      } finally {
        setTasksLoading(false)
      }
    }

    fetchTasks()
  }, [shoot.id, shoot.project_id, shoot.project?.client?.client_type])

  // Calculate task progress
  const getTaskProgress = () => {
    if (tasks.length === 0) return { completed: 0, total: 0, percentage: 0 }

    const completed = tasks.filter(task => task.status === 'completed').length
    const total = tasks.length
    const percentage = Math.round((completed / total) * 100)

    return { completed, total, percentage }
  }

  const taskProgress = getTaskProgress()

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-800'
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800'
      case 'cancelled':
        return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800'
      case 'rescheduled':
        return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800'
      default:
        return 'bg-muted text-muted-foreground border-border'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'cancelled':
        return <XCircle className="w-4 h-4" />
      case 'rescheduled':
        return <RotateCcw className="w-4 h-4" />
      default:
        return <AlertCircle className="w-4 h-4" />
    }
  }

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete this shoot? This action cannot be undone.`)) {
      return
    }

    setIsDeleting(true)
    try {
      await onDelete?.(shoot)
    } finally {
      setIsDeleting(false)
    }
  }

  const isUpcoming = new Date(shoot.scheduled_date) > new Date()
  const isPast = new Date(shoot.scheduled_date) < new Date()

  if (compact) {
    return (
      <div className="bg-card rounded-lg border border-border p-4 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-2">
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(shoot.status)}`}>
                {getStatusIcon(shoot.status)}
                <span className="ml-1 capitalize">{shoot.status}</span>
              </div>
              <div className="min-w-0 flex-1">
                <h4 className="font-medium text-card-foreground truncate">{shoot.project?.name}</h4>
                <p className="text-sm text-muted-foreground truncate">{shoot.project?.client?.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1 flex-shrink-0" />
                <span className="truncate">{formatDateTime(shoot.scheduled_date)}</span>
              </div>
              {shoot.project?.location && (
                <div className="flex items-center min-w-0">
                  <MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
                  <span className="truncate">{shoot.project.location}</span>
                </div>
              )}
            </div>

            {/* Task Progress Bar */}
            {tasks.length > 0 && (
              <div className="mt-3">
                <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
                  <div className="flex items-center">
                    <BarChart3 className="w-3 h-3 mr-1" />
                    <span>Tasks Progress</span>
                  </div>
                  <span>{taskProgress.completed}/{taskProgress.total} ({taskProgress.percentage}%)</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${taskProgress.percentage}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2 ml-4 flex-shrink-0">
            {/* Amount display */}
            {shoot.amount && (
              <div className="text-sm font-medium text-green-600 dark:text-green-400 mr-2">
                ₹{shoot.amount.toLocaleString()}
              </div>
            )}
            {/* Desktop buttons */}
            <div className="hidden sm:flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/shoots/${shoot.id}`)}
                className="h-8 w-8 p-0 hover:bg-gray-50 hover:border-gray-300 dark:hover:bg-gray-800"
                title="View shoot details"
              >
                <ExternalLink className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              </Button>
              {onEdit && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEdit(shoot)}
                  className="h-8 w-8 p-0 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950"
                  title="Edit shoot"
                >
                  <Edit className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </Button>
              )}
              {onDelete && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 hover:border-red-300 dark:hover:bg-red-950"
                  title="Delete shoot"
                >
                  {isDeleting ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                </Button>
              )}
            </div>

            {/* Mobile dropdown menu */}
            <div className="sm:hidden">
              <DropdownMenu
                trigger={
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0"
                  >
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                }
              >
                <DropdownMenuItem onClick={() => router.push(`/shoots/${shoot.id}`)}>
                  <ExternalLink className="w-4 h-4 mr-2 text-gray-600" />
                  View Details
                </DropdownMenuItem>
                {onStatusChange && shoot.status === 'scheduled' && (
                  <DropdownMenuItem onClick={() => onStatusChange(shoot, 'completed')}>
                    <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                    Mark Complete
                  </DropdownMenuItem>
                )}
                {onCancel && shoot.status === 'scheduled' && (
                  <DropdownMenuItem
                    onClick={() => onCancel(shoot)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel Shoot
                  </DropdownMenuItem>
                )}
                {onEdit && (
                  <DropdownMenuItem onClick={() => onEdit(shoot)}>
                    <Edit className="w-4 h-4 mr-2 text-blue-600" />
                    Edit Shoot
                  </DropdownMenuItem>
                )}
                {onDelete && (
                  <DropdownMenuItem
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    {isDeleting ? 'Deleting...' : 'Delete Shoot'}
                  </DropdownMenuItem>
                )}
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-card rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden border border-border">
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-card-foreground mb-1">
              {shoot.project?.name}
            </h3>
            <div className="flex items-center text-sm text-muted-foreground mb-2">
              <Building className="w-4 h-4 mr-1" />
              {shoot.project?.client?.name}
            </div>
          </div>
          <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(shoot.status)}`}>
            {getStatusIcon(shoot.status)}
            <span className="ml-1 capitalize">{shoot.status}</span>
          </div>
        </div>

        {/* Date and Time */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="w-4 h-4 mr-2" />
            <span className="font-medium">Scheduled:</span>
            <span className="ml-2">{formatDateTime(shoot.scheduled_date)}</span>
          </div>

          {shoot.actual_date && (
            <div className="flex items-center text-sm text-muted-foreground">
              <CheckCircle className="w-4 h-4 mr-2" />
              <span className="font-medium">Completed:</span>
              <span className="ml-2">{formatDateTime(shoot.actual_date)}</span>
            </div>
          )}

          {isUpcoming && (
            <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded">
              Upcoming shoot
            </div>
          )}

          {isPast && shoot.status === 'scheduled' && (
            <div className="text-xs text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-900/20 px-2 py-1 rounded">
              Overdue
            </div>
          )}
        </div>

        {/* Location */}
        {shoot.project?.location && (
          <div className="flex items-center text-sm text-muted-foreground mb-3">
            <MapPin className="w-4 h-4 mr-2" />
            <span className="truncate">{shoot.project.location}</span>
          </div>
        )}

        {/* Pilot */}
        {shoot.pilot && (
          <div className="flex items-center text-sm text-muted-foreground mb-3">
            <User className="w-4 h-4 mr-2" />
            <span>Pilot: {shoot.pilot.name}</span>
          </div>
        )}

        {/* Task Progress */}
        {tasks.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
              <div className="flex items-center">
                <BarChart3 className="w-4 h-4 mr-2" />
                <span>Tasks Progress</span>
              </div>
              <span className="font-medium">{taskProgress.completed}/{taskProgress.total} completed ({taskProgress.percentage}%)</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div
                className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${taskProgress.percentage}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Recurring Info */}
        {shoot.is_recurring && (
          <div className="flex items-center text-sm text-muted-foreground mb-3">
            <RotateCcw className="w-4 h-4 mr-2" />
            <span>Recurring: {shoot.recurring_pattern}</span>
          </div>
        )}

        {/* Notes */}
        {shoot.notes && (
          <div className="text-sm text-muted-foreground mb-3">
            <div className="flex items-start">
              <FileText className="w-4 h-4 mr-2 mt-0.5" />
              <p className="line-clamp-2">{shoot.notes}</p>
            </div>
          </div>
        )}

        {/* Weather Conditions */}
        {shoot.weather_conditions && (
          <div className="text-sm text-muted-foreground mb-3">
            <span className="font-medium">Weather:</span> {shoot.weather_conditions}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 bg-muted border-t border-border">
        <div className="flex items-center justify-between">
          <div className="text-xs text-muted-foreground">
            Created {formatDate(shoot.created_at)}
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/shoots/${shoot.id}`)}
              className="text-xs text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
              title="View shoot details"
            >
              <ExternalLink className="w-3 h-3 mr-1" />
              Details
            </Button>
            {onStatusChange && shoot.status === 'scheduled' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onStatusChange(shoot, 'completed')}
                className="text-xs text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-950"
              >
                <CheckCircle className="w-3 h-3 mr-1" />
                Mark Complete
              </Button>
            )}
            {onCancel && shoot.status === 'scheduled' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCancel(shoot)}
                className="text-xs text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-950"
                title="Cancel shoot"
              >
                <X className="w-3 h-3 mr-1" />
                Cancel
              </Button>
            )}
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(shoot)}
                className="text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-950"
                title="Edit shoot"
              >
                <Edit className="w-3 h-3 mr-1" />
                Edit
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
