'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Modal } from '@/components/ui/modal'
import { CheckCircle, Clock, Battery, Camera, Receipt, Plus, X } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useCreateExpense } from '@/hooks/useApi'
import toast from 'react-hot-toast'
import type { Shoot, ExpenseCategory } from '@/types'

interface ShootCompletionFormProps {
  shoot: Shoot | null
  isOpen: boolean
  onClose: () => void
  onComplete: (completionData: ShootCompletionData) => void
}

export interface ShootCompletionData {
  device_used: string
  battery_count: number
  shoot_start_time: string
  shoot_end_time: string
  completion_notes?: string
  expenses?: ExpenseData[]
}

export interface ExpenseData {
  description: string
  amount: number
  category: ExpenseCategory
  receipt_url?: string
}

export function ShootCompletionForm({ shoot, isOpen, onClose, onComplete }: ShootCompletionFormProps) {
  const { user } = useAuth()
  const { createExpense } = useCreateExpense()
  const [formData, setFormData] = useState<ShootCompletionData>({
    device_used: '',
    battery_count: 1,
    shoot_start_time: '',
    shoot_end_time: '',
    completion_notes: '',
    expenses: []
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showExpenseForm, setShowExpenseForm] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.device_used || !formData.shoot_start_time || !formData.shoot_end_time) {
      return
    }

    // Validate that end time is after start time
    if (formData.shoot_end_time <= formData.shoot_start_time) {
      toast.error('End time must be after start time')
      return
    }

    if (!user) {
      toast.error('You must be logged in to complete shoots')
      return
    }

    setIsSubmitting(true)
    try {
      // Complete the shoot first
      await onComplete(formData)

      // Create expenses if any
      if (formData.expenses && formData.expenses.length > 0) {
        for (const expense of formData.expenses) {
          await createExpense({
            description: expense.description,
            amount: expense.amount,
            category: expense.category,
            date: shoot?.scheduled_date ? new Date(shoot.scheduled_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
            project_id: shoot?.project_id || null,
            user_id: user.id,
            receipt_url: expense.receipt_url || null
          })
        }
        toast.success(`Shoot completed and ${formData.expenses.length} expense(s) recorded`)
      } else {
        toast.success('Shoot completed successfully')
      }

      onClose()
      // Reset form
      setFormData({
        device_used: '',
        battery_count: 1,
        shoot_start_time: '',
        shoot_end_time: '',
        completion_notes: '',
        expenses: []
      })
      setShowExpenseForm(false)
    } catch (error) {
      console.error('Error completing shoot:', error)
      toast.error('Failed to complete shoot')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: keyof ShootCompletionData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addExpense = () => {
    const newExpense: ExpenseData = {
      description: '',
      amount: 0,
      category: 'fuel_travel',
      receipt_url: ''
    }
    setFormData(prev => ({
      ...prev,
      expenses: [...(prev.expenses || []), newExpense]
    }))
    setShowExpenseForm(true)
  }

  const updateExpense = (index: number, field: keyof ExpenseData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      expenses: prev.expenses?.map((expense, i) =>
        i === index ? { ...expense, [field]: value } : expense
      ) || []
    }))
  }

  const removeExpense = (index: number) => {
    setFormData(prev => ({
      ...prev,
      expenses: prev.expenses?.filter((_, i) => i !== index) || []
    }))
    if (formData.expenses?.length === 1) {
      setShowExpenseForm(false)
    }
  }

  if (!shoot) return null

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Complete Shoot">
      <div className="space-y-6">
        {/* Shoot Info */}
        <div className="bg-muted p-4 rounded-lg">
          <h3 className="font-medium text-sm text-muted-foreground mb-2">Completing Shoot</h3>
          <p className="font-medium">{new Date(shoot.scheduled_date).toLocaleDateString()}</p>
          <p className="text-sm text-muted-foreground">{shoot.location}</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Device Used */}
          <div className="space-y-2">
            <Label htmlFor="device_used" className="flex items-center gap-2">
              <Camera className="w-4 h-4" />
              Device Used *
            </Label>
            <Input
              id="device_used"
              value={formData.device_used}
              onChange={(e) => handleInputChange('device_used', e.target.value)}
              placeholder="e.g., Canon EOS R5, DJI Mavic 3"
              required
            />
          </div>

          {/* Battery Count */}
          <div className="space-y-2">
            <Label htmlFor="battery_count" className="flex items-center gap-2">
              <Battery className="w-4 h-4" />
              Number of Batteries Used *
            </Label>
            <Input
              id="battery_count"
              type="number"
              min="1"
              value={formData.battery_count}
              onChange={(e) => handleInputChange('battery_count', parseInt(e.target.value) || 1)}
              required
            />
          </div>

          {/* Timing */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="shoot_start_time" className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Start Time *
              </Label>
              <Input
                id="shoot_start_time"
                type="time"
                value={formData.shoot_start_time}
                onChange={(e) => handleInputChange('shoot_start_time', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="shoot_end_time" className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                End Time *
              </Label>
              <Input
                id="shoot_end_time"
                type="time"
                value={formData.shoot_end_time}
                onChange={(e) => handleInputChange('shoot_end_time', e.target.value)}
                required
              />
            </div>
          </div>

          {/* Expenses Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="flex items-center gap-2">
                <Receipt className="w-4 h-4" />
                Shoot Expenses
              </Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addExpense}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Expense
              </Button>
            </div>

            {formData.expenses && formData.expenses.length > 0 && (
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {formData.expenses.map((expense, index) => (
                  <div key={index} className="border border-border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Expense {index + 1}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeExpense(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-xs">Description *</Label>
                        <Input
                          value={expense.description}
                          onChange={(e) => updateExpense(index, 'description', e.target.value)}
                          placeholder="Expense description"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label className="text-xs">Amount (₹) *</Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={expense.amount || ''}
                          onChange={(e) => updateExpense(index, 'amount', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                          className="mt-1"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-xs">Category *</Label>
                        <select
                          value={expense.category}
                          onChange={(e) => updateExpense(index, 'category', e.target.value as ExpenseCategory)}
                          className="mt-1 flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                        >
                          <option value="cymatics">Cymatics</option>
                          <option value="salary">Salary</option>
                          <option value="gadgets">Gadgets</option>
                          <option value="outsourcing">Outsourcing</option>
                          <option value="asset">Asset</option>
                          <option value="loan_repayment">Loan Repayment</option>
                          <option value="investments">Investments</option>
                          <option value="fuel_travel">Fuel & Travel</option>
                          <option value="food_snacks">Food & Snacks</option>
                          <option value="others">Others</option>
                          <option value="entertainment">Entertainment</option>
                          <option value="gopi">Gopi</option>
                          <option value="yaso">Yaso</option>
                          <option value="adithyan">Adithyan</option>
                        </select>
                      </div>
                      <div>
                        <Label className="text-xs">Receipt URL</Label>
                        <Input
                          type="url"
                          value={expense.receipt_url || ''}
                          onChange={(e) => updateExpense(index, 'receipt_url', e.target.value)}
                          placeholder="https://..."
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Completion Notes */}
          <div className="space-y-2">
            <Label htmlFor="completion_notes">Additional Notes</Label>
            <Textarea
              id="completion_notes"
              value={formData.completion_notes}
              onChange={(e) => handleInputChange('completion_notes', e.target.value)}
              placeholder="Any additional notes about the shoot completion..."
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                isSubmitting ||
                !formData.device_used ||
                !formData.shoot_start_time ||
                !formData.shoot_end_time ||
                (formData.expenses && formData.expenses.some(exp => !exp.description || !exp.amount))
              }
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b border-white mr-2"></div>
                  Completing...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Complete Shoot
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  )
}
