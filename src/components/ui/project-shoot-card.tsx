'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { tasksApi, projectsApi } from '@/lib/api'
import {
  Calendar,
  Clock,
  User,
  MapPin,
  Edit,
  CheckCircle,
  XCircle,
  MoreHorizontal,
  ArrowRight,
  BarChart3
} from 'lucide-react'
import type { Shoot, Task } from '@/types'

interface ProjectShootCardProps {
  shoot: Shoot
  onEdit?: (shoot: Shoot) => void
  onStatusChange?: (shoot: Shoot, status: string) => void
  onCancel?: (shoot: Shoot) => void
}

export function ProjectShootCard({ shoot, onEdit, onStatusChange, onCancel }: ProjectShootCardProps) {
  const router = useRouter()
  const [tasks, setTasks] = useState<Task[]>([])
  const [tasksLoading, setTasksLoading] = useState(false)

  // Fetch tasks for this shoot
  useEffect(() => {
    const fetchTasks = async () => {
      try {
        setTasksLoading(true)
        const allTasks = await tasksApi.getAll()
        // Include both shoot-specific tasks and project-level tasks (default tasks)
        const shootTasks = allTasks.filter(task =>
          task.shoot_id === shoot.id ||
          (task.project_id === shoot.project_id && !task.shoot_id)
        )

        // If no tasks exist for this project and we have client type, create default tasks
        if (shootTasks.length === 0 && shoot.project?.client?.client_type) {
          try {
            await projectsApi.createDefaultTasks(
              shoot.project_id,
              shoot.project.client.client_type,
              shoot.scheduled_date
            )

            // Refetch tasks after creating default ones
            const updatedTasks = await tasksApi.getAll()
            const updatedShootTasks = updatedTasks.filter(task =>
              task.shoot_id === shoot.id ||
              (task.project_id === shoot.project_id && !task.shoot_id)
            )
            setTasks(updatedShootTasks)
          } catch (taskError) {
            console.error('Failed to create default tasks:', taskError)
            setTasks(shootTasks) // Set empty tasks if creation fails
          }
        } else {
          setTasks(shootTasks)
        }
      } catch (error) {
        console.error('Error fetching tasks for shoot:', error)
      } finally {
        setTasksLoading(false)
      }
    }

    fetchTasks()
  }, [shoot.id, shoot.project_id, shoot.project?.client?.client_type])

  // Calculate task progress
  const getTaskProgress = () => {
    if (tasks.length === 0) return { completed: 0, total: 0, percentage: 0 }
    
    const completed = tasks.filter(task => task.status === 'completed').length
    const total = tasks.length
    const percentage = Math.round((completed / total) * 100)
    
    return { completed, total, percentage }
  }

  const taskProgress = getTaskProgress()

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-700 border-blue-200'
      case 'completed':
        return 'bg-green-100 text-green-700 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-700 border-red-200'
      case 'rescheduled':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock className="w-3 h-3" />
      case 'completed':
        return <CheckCircle className="w-3 h-3" />
      case 'cancelled':
        return <XCircle className="w-3 h-3" />
      default:
        return <Clock className="w-3 h-3" />
    }
  }

  const handleCardClick = () => {
    router.push(`/shoots/${shoot.id}`)
  }

  const isUpcoming = new Date(shoot.scheduled_date) > new Date()
  const isPast = new Date(shoot.scheduled_date) < new Date()

  return (
    <div 
      className="group bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200 hover:shadow-lg cursor-pointer"
      onClick={handleCardClick}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-2">
              <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getStatusColor(shoot.status)}`}>
                {getStatusIcon(shoot.status)}
                <span className="ml-1.5 capitalize">{shoot.status}</span>
              </div>
              {isPast && shoot.status === 'scheduled' && (
                <div className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-700 border border-orange-200">
                  Overdue
                </div>
              )}
            </div>
            
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-1">
              <Calendar className="w-4 h-4 mr-2 flex-shrink-0" />
              <span className="font-medium">
                {new Date(shoot.scheduled_date).toLocaleDateString('en-US', {
                  weekday: 'short',
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric'
                })}
              </span>
              <span className="mx-2">•</span>
              <span>
                {new Date(shoot.scheduled_date).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </div>
          </div>

          {/* Amount */}
          {shoot.amount && (
            <div className="text-right">
              <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                ₹{shoot.amount.toLocaleString()}
              </div>
            </div>
          )}
        </div>

        {/* Details */}
        <div className="space-y-2 mb-4">
          {shoot.pilot && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <User className="w-4 h-4 mr-2 flex-shrink-0" />
              <span>Pilot: {shoot.pilot.name}</span>
            </div>
          )}
          
          {shoot.location && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <MapPin className="w-4 h-4 mr-2 flex-shrink-0" />
              <span className="truncate">{shoot.location}</span>
            </div>
          )}
        </div>

        {/* Task Progress */}
        {tasks.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-2">
              <div className="flex items-center">
                <BarChart3 className="w-3 h-3 mr-1" />
                <span>Progress</span>
              </div>
              <span className="font-medium">{taskProgress.completed}/{taskProgress.total} tasks</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  taskProgress.percentage === 100 
                    ? 'bg-green-500' 
                    : taskProgress.percentage > 50 
                      ? 'bg-blue-500' 
                      : 'bg-gray-400'
                }`}
                style={{ width: `${taskProgress.percentage}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {taskProgress.percentage}% complete
            </div>
          </div>
        )}

        {/* Notes */}
        {shoot.notes && (
          <div className="mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
              {shoot.notes}
            </p>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center space-x-2">
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onEdit(shoot)
                }}
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <Edit className="w-3 h-3" />
              </Button>
            )}
            
            {onStatusChange && shoot.status === 'scheduled' && (
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onStatusChange(shoot, 'completed')
                }}
                className="h-8 px-3 text-xs text-green-600 hover:text-green-700 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <CheckCircle className="w-3 h-3 mr-1" />
                Complete
              </Button>
            )}
          </div>

          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
            <span className="mr-1">View Details</span>
            <ArrowRight className="w-3 h-3" />
          </div>
        </div>
      </div>
    </div>
  )
}
