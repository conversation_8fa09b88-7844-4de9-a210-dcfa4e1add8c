'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { formatDate } from '@/lib/utils'
import {
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Circle,
  Play,
  User,
  Calendar,
  Building,
  Camera,
  Edit,
  Trash2,
  Flag,
  Target
} from 'lucide-react'
import type { Task, Shoot } from '@/types'

interface TaskCardProps {
  task: Task
  onEdit?: (task: Task) => void
  onDelete?: (task: Task) => void
  onStatusChange?: (task: Task, newStatus: Task['status']) => void
  onCompleteShoot?: (shoot: Shoot) => void
  shoot?: Shoot
  compact?: boolean
}

export function TaskCard({ task, onEdit, onDelete, onStatusChange, onCompleteShoot, shoot, compact = false }: TaskCardProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Circle className="w-4 h-4" />
      case 'in_progress':
        return <Play className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'cancelled':
        return <XCircle className="w-4 h-4" />
      default:
        return <Circle className="w-4 h-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-muted text-muted-foreground border-border'
      case 'in_progress':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-800'
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800'
      case 'cancelled':
        return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800'
      default:
        return 'bg-muted text-muted-foreground border-border'
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <AlertTriangle className="w-4 h-4" />
      case 'high':
        return <Flag className="w-4 h-4" />
      case 'medium':
        return <Flag className="w-4 h-4" />
      case 'low':
        return <Flag className="w-4 h-4" />
      default:
        return <Flag className="w-4 h-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600'
      case 'high':
        return 'text-orange-600'
      case 'medium':
        return 'text-yellow-600'
      case 'low':
        return 'text-green-600'
      default:
        return 'text-muted-foreground'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pending'
      case 'in_progress':
        return 'In Progress'
      case 'completed':
        return 'Completed'
      case 'cancelled':
        return 'Cancelled'
      default:
        return status
    }
  }

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'Urgent'
      case 'high':
        return 'High'
      case 'medium':
        return 'Medium'
      case 'low':
        return 'Low'
      default:
        return priority
    }
  }

  const isOverdue = task.due_date && new Date(task.due_date) < new Date() && task.status !== 'completed'

  const handleStatusChange = async (newStatus: Task['status']) => {
    setIsUpdatingStatus(true)
    try {
      await onStatusChange?.(task, newStatus)
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete the task "${task.title}"? This action cannot be undone.`)) {
      return
    }

    setIsDeleting(true)
    try {
      await onDelete?.(task)
    } finally {
      setIsDeleting(false)
    }
  }

  if (compact) {
    return (
      <div className={`bg-card rounded-lg border p-4 hover:shadow-md transition-shadow ${isOverdue ? 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20' : 'border-border'}`}>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3">
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                {getStatusIcon(task.status)}
                <span className="ml-1">{getStatusLabel(task.status)}</span>
              </div>
              <div className={`flex items-center ${getPriorityColor(task.priority)}`}>
                {getPriorityIcon(task.priority)}
                <span className="ml-1 text-xs font-medium">{getPriorityLabel(task.priority)}</span>
              </div>
            </div>
            <div className="mt-2">
              <h4 className="font-medium text-card-foreground">{task.title}</h4>
              {task.description && (
                <p className="text-sm text-muted-foreground line-clamp-1">{task.description}</p>
              )}
            </div>
            <div className="mt-2 flex items-center space-x-4 text-sm text-muted-foreground">
              {task.due_date && (
                <div className={`flex items-center ${isOverdue ? 'text-red-600' : ''}`}>
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatDate(task.due_date)}
                </div>
              )}
              {task.project && (
                <div className="flex items-center">
                  <Building className="w-3 h-3 mr-1" />
                  {task.project.name}
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {/* Show Complete Shoot button if this task is related to a shoot and shoot is not completed */}
            {shoot && shoot.status !== 'completed' && onCompleteShoot && task.title.toLowerCase().includes('shoot') && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCompleteShoot(shoot)}
                className="text-green-600 hover:text-green-700"
                title="Complete Shoot"
              >
                <Target className="w-4 h-4" />
              </Button>
            )}
            {task.status !== 'completed' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleStatusChange(task.status === 'pending' ? 'in_progress' : 'completed')}
                disabled={isUpdatingStatus}
              >
                {task.status === 'pending' ? <Play className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
              </Button>
            )}
            {onEdit && (
              <Button variant="outline" size="sm" onClick={() => onEdit(task)}>
                <Edit className="w-4 h-4" />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                disabled={isDeleting}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-card rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden ${isOverdue ? 'border-l-4 border-red-500' : ''}`}>
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                {getStatusIcon(task.status)}
                <span className="ml-1">{getStatusLabel(task.status)}</span>
              </div>
              <div className={`flex items-center ${getPriorityColor(task.priority)}`}>
                {getPriorityIcon(task.priority)}
                <span className="ml-1 text-xs font-medium">{getPriorityLabel(task.priority)}</span>
              </div>
              {isOverdue && (
                <div className="flex items-center text-red-600">
                  <Clock className="w-4 h-4 mr-1" />
                  <span className="text-xs font-medium">Overdue</span>
                </div>
              )}
            </div>
            <h3 className="text-lg font-semibold text-card-foreground mb-1">
              {task.title}
            </h3>
            {task.description && (
              <p className="text-sm text-muted-foreground line-clamp-2">{task.description}</p>
            )}
          </div>
        </div>

        {/* Task Details */}
        <div className="space-y-2 mb-4">
          {task.due_date && (
            <div className={`flex items-center text-sm ${isOverdue ? 'text-red-600 dark:text-red-400' : 'text-muted-foreground'}`}>
              <Calendar className="w-4 h-4 mr-2" />
              <span className="font-medium">Due:</span>
              <span className="ml-2">{formatDate(task.due_date)}</span>
            </div>
          )}

          {task.assigned_to && (
            <div className="flex items-center text-sm text-muted-foreground">
              <User className="w-4 h-4 mr-2" />
              <span className="font-medium">Assigned to:</span>
              <span className="ml-2">{task.assigned_to}</span>
            </div>
          )}

          {task.project && (
            <div className="flex items-center text-sm text-muted-foreground">
              <Building className="w-4 h-4 mr-2" />
              <span className="font-medium">Project:</span>
              <span className="ml-2">{task.project.name}</span>
            </div>
          )}

          {task.shoot && (
            <div className="flex items-center text-sm text-muted-foreground">
              <Camera className="w-4 h-4 mr-2" />
              <span className="font-medium">Shoot:</span>
              <span className="ml-2">{task.shoot.title}</span>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        {task.status !== 'completed' && task.status !== 'cancelled' && (
          <div className="flex items-center space-x-2 mb-3">
            {task.status === 'pending' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleStatusChange('in_progress')}
                disabled={isUpdatingStatus}
                className="text-xs"
              >
                <Play className="w-3 h-3 mr-1" />
                Start Task
              </Button>
            )}
            {task.status === 'in_progress' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleStatusChange('completed')}
                disabled={isUpdatingStatus}
                className="text-xs"
              >
                <CheckCircle className="w-3 h-3 mr-1" />
                Mark Complete
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 bg-muted border-t border-border">
        <div className="flex items-center justify-between">
          <div className="text-xs text-muted-foreground">
            Created {formatDate(task.created_at)}
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Show Complete Shoot button if this task is related to a shoot and shoot is not completed */}
            {shoot && shoot.status !== 'completed' && onCompleteShoot && task.title.toLowerCase().includes('shoot') && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCompleteShoot(shoot)}
                className="text-xs text-green-600 hover:text-green-700"
                title="Complete Shoot"
              >
                <Target className="w-3 h-3" />
              </Button>
            )}
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(task)}
                className="text-xs"
              >
                <Edit className="w-3 h-3" />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                disabled={isDeleting}
                className="text-xs text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
