'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useCreateExpense, useUpdateExpense, useProjects } from '@/hooks/useApi'
import { useAuth } from '@/contexts/AuthContext'
import toast from 'react-hot-toast'
import type { Expense } from '@/types'
import { EXPENSE_CATEGORIES } from '@/lib/constants'

const expenseSchema = z.object({
  description: z.string()
    .min(1, 'Description is required')
    .max(500, 'Description must be less than 500 characters')
    .trim(),
  amount: z.string()
    .min(1, 'Amount is required')
    .refine(
      (val) => {
        const num = Number(val)
        return !isNaN(num) && num > 0 && num <= 10000000 // Max 1 crore
      },
      'Amount must be a positive number less than ₹1,00,00,000'
    ),
  category: z.enum(['cymatics', 'salary', 'gadgets', 'outsourcing', 'asset', 'loan_repayment', 'investments', 'fuel_travel', 'food_snacks', 'others', 'entertainment', 'gopi', 'yaso', 'adithyan']),
  date: z.string()
    .min(1, 'Date is required')
    .refine(
      (val) => {
        const date = new Date(val)
        const now = new Date()
        const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
        const oneMonthFuture = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate())
        return date >= oneYearAgo && date <= oneMonthFuture
      },
      'Date must be within the last year and not more than 1 month in the future'
    ),
  project_id: z.string().optional(),
  receipt_url: z.string().optional().refine(
    (val) => {
      if (!val || val.trim() === '') return true
      try {
        new URL(val)
        return true
      } catch {
        return false
      }
    },
    'Receipt URL must be a valid URL'
  ),
})

type ExpenseFormData = z.infer<typeof expenseSchema>

interface ExpenseFormProps {
  expense?: Expense
  onSuccess?: (expense: Expense) => void
  onCancel?: () => void
  preselectedProjectId?: string
}

export function ExpenseForm({ expense, onSuccess, onCancel, preselectedProjectId }: ExpenseFormProps) {
  const isEditing = !!expense
  const { user } = useAuth()
  const { createExpense, loading: createLoading } = useCreateExpense()
  const { updateExpense, loading: updateLoading } = useUpdateExpense()
  const { data: projects, loading: projectsLoading } = useProjects()
  const loading = createLoading || updateLoading

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ExpenseFormData>({
    resolver: zodResolver(expenseSchema),
    defaultValues: expense ? {
      description: expense.description,
      amount: expense.amount.toString(),
      category: (expense.category as any) === 'fuel' ? 'fuel_travel' : expense.category as any, // Handle legacy data
      date: new Date(expense.date).toISOString().slice(0, 10),
      project_id: expense.project_id || '',
      receipt_url: expense.receipt_url || '',
    } : {
      description: '',
      amount: '',
      category: 'fuel',
      date: new Date().toISOString().slice(0, 10),
      project_id: preselectedProjectId || '',
      receipt_url: '',
    },
  })

  const onSubmit = async (data: ExpenseFormData) => {
    try {
      if (!user) {
        toast.error('You must be logged in to record expenses')
        return
      }

      // Additional validation
      const amount = parseFloat(data.amount)
      if (isNaN(amount) || amount <= 0) {
        toast.error('Please enter a valid amount')
        return
      }

      if (amount > 10000000) {
        toast.error('Amount cannot exceed ₹1,00,00,000')
        return
      }

      const expenseData = {
        ...data,
        amount,
        project_id: data.project_id?.trim() || null,
        receipt_url: data.receipt_url?.trim() || null,
        user_id: user.id,
        description: data.description.trim(),
      }

      let result: Expense
      if (isEditing) {
        if (!expense?.id) {
          toast.error('Invalid expense data')
          return
        }
        result = await updateExpense(expense.id, expenseData)
        toast.success('Expense updated successfully')
      } else {
        result = await createExpense(expenseData)
        toast.success('Expense recorded successfully')
        reset()
      }

      onSuccess?.(result)
    } catch (error: any) {
      console.error('Error saving expense:', error)

      // Handle specific error types
      if (error.message?.includes('duplicate')) {
        toast.error('This expense may already exist')
      } else if (error.message?.includes('permission')) {
        toast.error('You do not have permission to perform this action')
      } else if (error.message?.includes('network')) {
        toast.error('Network error. Please check your connection and try again')
      } else {
        toast.error(error.message || 'Failed to save expense. Please try again')
      }
    }
  }

  if (projectsLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <Label htmlFor="description">Description *</Label>
        <Input
          id="description"
          {...register('description')}
          placeholder="Enter expense description"
          className="mt-1"
        />
        {errors.description && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.description.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="amount">Amount (₹) *</Label>
        <Input
          id="amount"
          type="number"
          step="0.01"
          min="0"
          {...register('amount')}
          placeholder="Enter expense amount"
          className="mt-1"
        />
        {errors.amount && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.amount.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="category">Category *</Label>
        <select
          id="category"
          {...register('category')}
          className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          {EXPENSE_CATEGORIES.map((category) => (
            <option key={category.value} value={category.value}>
              {category.label}
            </option>
          ))}
        </select>
        {errors.category && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.category.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="date">Date *</Label>
        <Input
          id="date"
          type="date"
          {...register('date')}
          className="mt-1"
        />
        {errors.date && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.date.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="project_id">Project (Optional)</Label>
        <select
          id="project_id"
          {...register('project_id')}
          className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <option value="">No specific project</option>
          {projects?.map((project) => (
            <option key={project.id} value={project.id}>
              {project.name} - {project.client?.name}
            </option>
          ))}
        </select>
        <p className="text-xs text-muted-foreground mt-1">
          Link this expense to a specific project (optional)
        </p>
        {errors.project_id && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.project_id.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="receipt_url">Receipt URL</Label>
        <Input
          id="receipt_url"
          type="url"
          {...register('receipt_url')}
          placeholder="https://example.com/receipt.jpg"
          className="mt-1"
        />
        <p className="text-xs text-muted-foreground mt-1">
          Upload receipt to cloud storage and paste the URL here
        </p>
        {errors.receipt_url && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.receipt_url.message}</p>
        )}
      </div>

      <div className="flex gap-3 pt-4">
        <Button
          type="submit"
          disabled={loading}
          className="flex-1"
        >
          {loading ? 'Saving...' : isEditing ? 'Update Expense' : 'Record Expense'}
        </Button>
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
        )}
      </div>
    </form>
  )
}
