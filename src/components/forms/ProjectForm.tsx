'use client'

import { useEffect, useCallback, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ClientSelector } from '@/components/ui/client-selector'
import { ContactPersonSelector } from '@/components/ui/contact-person-selector'
import { useCreateProject, useUpdateProject } from '@/hooks/useApi'
import { createDebouncedLocationExtractor } from '@/lib/maps-utils'
import toast from 'react-hot-toast'
import type { Project } from '@/types'

const projectSchema = z.object({
  name: z.string().min(1, 'Project name is required'),
  description: z.string().optional(),
  client_id: z.string().min(1, 'Client is required'),
  contact_person_id: z.string().optional(),
  location: z.string().optional(),
  google_maps_link: z.string().optional().refine((val) => {
    if (!val || val.trim() === '') return true
    try {
      new URL(val)
      return true
    } catch {
      return false
    }
  }, {
    message: 'Invalid URL'
  }),
})

type ProjectFormData = z.infer<typeof projectSchema>

interface ProjectFormProps {
  project?: Project
  onSuccess?: (project: Project) => void
  onCancel?: () => void
  preselectedClientId?: string
}

export function ProjectForm({ project, onSuccess, onCancel, preselectedClientId }: ProjectFormProps) {
  const isEditing = !!project
  const { createProject, loading: createLoading } = useCreateProject()
  const { updateProject, loading: updateLoading } = useUpdateProject()
  const loading = createLoading || updateLoading
  const [extractingLocation, setExtractingLocation] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: project ? {
      name: project.name,
      description: project.description || '',
      client_id: project.client_id,
      contact_person_id: project.contact_person_id || '',
      location: project.location || '',
      google_maps_link: project.google_maps_link || '',
    } : {
      name: '',
      description: '',
      client_id: preselectedClientId || '',
      contact_person_id: '',
      location: '',
      google_maps_link: '',
    },
  })

  // Watch for changes in Google Maps link
  const googleMapsLink = watch('google_maps_link')

  // Create debounced location extractor
  const debouncedExtractLocation = useCallback(
    createDebouncedLocationExtractor(1500),
    []
  )

  // Extract location when Google Maps link changes
  useEffect(() => {
    if (googleMapsLink && googleMapsLink.trim()) {
      setExtractingLocation(true)
      debouncedExtractLocation(googleMapsLink, (locationInfo) => {
        setExtractingLocation(false)
        if (locationInfo && locationInfo.formattedLocation) {
          setValue('location', locationInfo.formattedLocation)
          toast.success(`Location extracted: ${locationInfo.formattedLocation}`)
        } else {
          // Check if it's a shortened URL
          const isShortenedUrl = googleMapsLink.includes('goo.gl') || googleMapsLink.includes('maps.app.goo.gl')
          if (isShortenedUrl) {
            toast.error('Unable to extract location from shortened URL. Please wait while we attempt to resolve it, or enter location manually.')
          } else {
            toast.error('Could not extract location from the provided link. Please check the URL format or enter location manually.')
          }
        }
      })
    } else {
      setExtractingLocation(false)
    }
  }, [googleMapsLink, debouncedExtractLocation, setValue, setExtractingLocation])

  const onSubmit = async (data: ProjectFormData) => {
    try {
      // Convert empty strings to null for optional fields
      const cleanData = {
        ...data,
        description: data.description || null,
        contact_person_id: data.contact_person_id || null,
        location: data.location || null,
        google_maps_link: data.google_maps_link || null,
        total_amount: 0, // Will be calculated from shoots
        gst_inclusive: false, // Will be determined by client GST status
        amount_received: 0,
        amount_pending: 0,
      }

      let result: Project
      if (isEditing) {
        result = await updateProject(project.id, cleanData)
        toast.success('Project updated successfully')
      } else {
        result = await createProject(cleanData)
        toast.success('Project created successfully')
        reset()
      }

      onSuccess?.(result)
    } catch (error: any) {
      toast.error(error.message || 'Failed to save project')
    }
  }

  return (
    <div className="modern-card overflow-hidden">
      {/* Form Header */}
      <div className="bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border-b border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow-lg">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">
              {isEditing ? 'Edit Project' : 'Create New Project'}
            </h3>
            <p className="text-sm text-muted-foreground">
              {isEditing ? 'Update project information' : 'Add a new project to your portfolio'}
            </p>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
        {/* Project Name */}
        <div className="space-y-2">
          <Label htmlFor="name" className="text-sm font-medium text-foreground">Project Name *</Label>
          <Input
            id="name"
            {...register('name')}
            placeholder="Enter project name"
            className="modern-input"
          />
          {errors.name && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {errors.name.message}
            </p>
          )}
        </div>

        {/* Client Selection */}
        <div className="bg-gradient-to-r from-green-500/5 to-emerald-500/5 rounded-lg p-4 border border-green-500/20">
          <ClientSelector
            value={watch('client_id')}
            onChange={(clientId) => {
              setValue('client_id', clientId)
              setValue('contact_person_id', '') // Reset contact person when client changes
            }}
            error={errors.client_id?.message}
            disabled={!!preselectedClientId}
            label="Client"
            required
          />
        </div>

        {/* Contact Person */}
        <div className="space-y-2">
          <ContactPersonSelector
            clientId={watch('client_id')}
            value={watch('contact_person_id')}
            onChange={(contactPersonId) => setValue('contact_person_id', contactPersonId)}
            error={errors.contact_person_id?.message}
            label="Primary Contact"
          />
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="description" className="text-sm font-medium text-foreground">Description</Label>
          <textarea
            id="description"
            {...register('description')}
            placeholder="Enter project description"
            className="w-full px-4 py-3 bg-white/50 dark:bg-black/20 border border-gray-200 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500/50 transition-all duration-200 backdrop-blur-sm resize-none min-h-[100px] text-sm placeholder:text-muted-foreground"
            rows={4}
          />
          {errors.description && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {errors.description.message}
            </p>
          )}
        </div>

        {/* Location Section */}
        <div className="bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-lg p-4 border border-purple-500/20 space-y-4">
          <div className="flex items-center space-x-2 mb-3">
            <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <h4 className="font-medium text-foreground">Location Details</h4>
          </div>

          <div className="space-y-2">
            <Label htmlFor="location" className="text-sm font-medium text-foreground">Location</Label>
            <div className="relative">
              <Input
                id="location"
                {...register('location')}
                placeholder="Enter project location (or paste Google Maps link below)"
                className="modern-input"
              />
              {extractingLocation && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-purple-200 dark:border-purple-800 border-t-purple-600 dark:border-t-purple-400"></div>
                </div>
              )}
            </div>
            {errors.location && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {errors.location.message}
              </p>
            )}
            {extractingLocation && (
              <p className="text-sm text-purple-600 dark:text-purple-400 flex items-center">
                <svg className="w-4 h-4 mr-1 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Extracting location from Google Maps link...
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="google_maps_link" className="text-sm font-medium text-foreground">Google Maps Link</Label>
            <Input
              id="google_maps_link"
              {...register('google_maps_link')}
              placeholder="https://maps.google.com/... (location will be auto-extracted)"
              className="modern-input"
            />
            {errors.google_maps_link && (
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {errors.google_maps_link.message}
              </p>
            )}
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-800">
              <p className="text-xs text-purple-700 dark:text-purple-300">
                💡 <strong>Tip:</strong> Paste a Google Maps link and the location will be automatically extracted above.
                <br />
                For best results, use the full URL from your browser's address bar instead of shortened links.
              </p>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
              className="px-6 py-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            disabled={loading}
            className="btn-gradient-blue px-8 py-2 min-w-[140px]"
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/30 border-t-white mr-2"></div>
                Saving...
              </div>
            ) : (
              isEditing ? 'Update Project' : 'Create Project'
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
