export type UserRole = 'admin' | 'manager' | 'pilot' | 'editor'

export interface User {
  id: string
  email: string
  name: string
  role: UserRole
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface ContactPerson {
  id: string
  client_id: string
  name: string
  phone?: string
  email?: string
  designation?: string
  is_primary: boolean
  created_at: string
  updated_at: string
}

export interface Client {
  id: string
  name: string
  email?: string
  phone?: string
  address?: string
  gst_number?: string
  has_gst: boolean
  client_type?: string
  notes?: string
  contact_persons?: ContactPerson[]
  created_at: string
  updated_at: string
}

export interface OutsourcingVendor {
  id: string
  name: string
  email?: string
  phone?: string
  address?: string
  contact_person?: string
  specialization?: string
  notes?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Project {
  id: string
  name: string
  description?: string
  client_id: string
  client?: Client
  contact_person_id?: string
  contact_person?: ContactPerson
  location?: string
  google_maps_link?: string
  status: 'active' | 'completed' | 'on_hold' | 'cancelled'
  total_amount: number
  gst_inclusive: boolean
  amount_received: number
  amount_pending: number
  vendor_payment_status?: 'pending' | 'paid' | 'overdue' | 'cancelled'
  vendor_payment_amount?: number
  vendor_payment_due_date?: string
  vendor_payment_date?: string
  vendor_payment_notes?: string
  created_at: string
  updated_at: string
  shoots?: Shoot[]
}

export interface Shoot {
  id: string
  project_id: string
  project?: Project
  scheduled_date: string
  scheduled_end_date?: string
  actual_date?: string
  status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled'
  pilot_id?: string
  pilot?: User
  amount: number
  location?: string
  google_maps_link?: string
  notes?: string
  weather_conditions?: string
  onedrive_folder_path?: string
  is_recurring: boolean
  recurring_pattern?: 'weekly' | 'monthly' | 'quarterly'
  is_outsourced?: boolean
  vendor_id?: string
  vendor?: OutsourcingVendor
  outsourcing_cost?: number
  device_used?: string
  battery_count?: number
  shoot_start_time?: string
  shoot_end_time?: string
  completion_notes?: string
  created_at: string
  updated_at: string
}

export type ExpenseCategory = 
  | 'Equipment'
  | 'Travel'
  | 'Accommodation'
  | 'Crew'
  | 'Location'
  | 'Permits'
  | 'Insurance'
  | 'Post Production'
  | 'Marketing'
  | 'Other';

export interface Expense {
  id: string
  project_id: string
  category: ExpenseCategory
  description: string
  amount: number
  date: string
  receipt_url?: string
  created_at: string
  updated_at: string
}

export interface Task {
  id: string
  title: string
  description?: string
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'skipped'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  assigned_to: string
  assigned_user?: User
  assigned_role?: 'pilot' | 'editor' | 'accounts'
  project_id?: string
  project?: Project
  shoot_id?: string
  shoot?: Shoot
  due_date?: string
  completed_at?: string
  comments?: string
  created_at: string
  updated_at: string
}

export interface Payment {
  id: string
  project_id: string
  project?: Project
  amount: number
  payment_date: string
  payment_method: 'cash' | 'bank_transfer' | 'credit_card' | 'cheque' | 'other'
  reference_number?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface Notification {
  id: string
  user_id: string
  title: string
  message: string
  type: 'shoot_reminder' | 'payment_overdue' | 'task_deadline' | 'general'
  read: boolean
  created_at: string
}

export interface ActivityLog {
  id: string
  user_id: string
  user?: User
  action: string
  entity_type: 'project' | 'shoot' | 'client' | 'expense' | 'task' | 'payment'
  entity_id: string
  details?: Record<string, any>
  created_at: string
}

// Form types
export interface CreateContactPersonForm {
  name: string
  phone?: string
  email?: string
  designation?: string
  is_primary?: boolean
}

export interface CreateClientForm {
  name: string
  email?: string
  phone?: string
  address?: string
  gst_number?: string
  has_gst: boolean
  client_type?: string
  notes?: string
  contact_persons?: CreateContactPersonForm[]
}

export interface CreateProjectForm {
  name: string
  description?: string
  client_id: string
  contact_person_id?: string
  location?: string
  google_maps_link?: string
}

export interface CreateShootForm {
  project_id: string
  scheduled_date: string
  scheduled_end_date?: string
  pilot_id?: string
  amount: number
  location?: string
  google_maps_link?: string
  notes?: string
  is_recurring: boolean
  recurring_pattern?: 'weekly' | 'monthly' | 'quarterly'
  is_outsourced?: boolean
  vendor_id?: string
  outsourcing_cost?: number
}

export interface CreateExpenseForm {
  description: string
  amount: number
  category: 'fuel' | 'equipment' | 'travel' | 'maintenance' | 'other'
  date: string
  project_id?: string
}

export interface CreateTaskForm {
  title: string
  description?: string
  status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  assigned_to: string
  project_id?: string
  shoot_id?: string
  due_date?: string
}

// API Response types
export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// Dashboard stats
export interface DashboardStats {
  totalProjects: number
  activeProjects: number
  completedShoots: number
  pendingPayments: number
  totalRevenue: number
  monthlyRevenue: number
  upcomingShoots: number
  overdueTasks: number
  vendorPayments: {
    totalPending: number
    totalOverdue: number
    pendingCount: number
    overdueCount: number
  }
}
