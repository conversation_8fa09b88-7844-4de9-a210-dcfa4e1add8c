import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const vendorId = searchParams.get('vendorId')
    
    if (!vendorId) {
      return NextResponse.json({ error: 'vendorId is required' }, { status: 400 })
    }
    
    const supabase = await createServerSupabaseClient()
    
    // Fetch shoots for this vendor with project and client information
    const { data: shoots, error } = await supabase
      .from('shoots')
      .select(`
        *,
        project:projects(
          *,
          client:clients(name),
          payments:payments(*)
        )
      `)
      .eq('vendor_id', vendorId)
      .eq('is_outsourced', true)
      .order('scheduled_date', { ascending: false })

    if (error) {
      console.error('Error fetching vendor shoots:', error)
      return NextResponse.json({ 
        error: 'Failed to fetch vendor shoots', 
        details: error.message 
      }, { status: 500 })
    }
    
    return NextResponse.json({
      shoots: shoots || [],
      count: shoots?.length || 0
    })
    
  } catch (error: any) {
    console.error('API error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 })
  }
}
