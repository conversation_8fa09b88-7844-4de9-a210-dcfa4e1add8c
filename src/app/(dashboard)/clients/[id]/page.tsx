'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Modal } from '@/components/ui/modal'
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { ClientForm } from '@/components/forms/ClientForm'
import { ProjectForm } from '@/components/forms/ProjectForm'
import { ContactPersonsList } from '@/components/ui/ContactPersonsList'
import { ProjectCard } from '@/components/ui/project-card'
import { PaymentForm } from '@/components/forms/PaymentForm'
import { clientsApi, projectsApi, paymentsApi, tasksApi } from '@/lib/api'
import { ArrowLeft, Edit, Mail, Phone, MapPin, FileText, DollarSign, TrendingUp, Clock, Plus, ArrowUpDown, FolderOpen, Share2, MoreVertical, Trash2 } from 'lucide-react'
import toast from 'react-hot-toast'
import type { Client, Project, Payment, Task } from '@/types'

export default function ClientDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const clientId = params.id as string

  const [client, setClient] = useState<Client | null>(null)
  const [projects, setProjects] = useState<Project[]>([])
  const [payments, setPayments] = useState<Payment[]>([])
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isAddProjectModalOpen, setIsAddProjectModalOpen] = useState(false)
  const [isAddPaymentModalOpen, setIsAddPaymentModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [sortBy, setSortBy] = useState<'new_to_old' | 'pending_payment' | 'project_status'>('new_to_old')

  const fetchClientData = async () => {
    try {
      setLoading(true)
      const [clientData, projectsData, paymentsData, tasksData] = await Promise.all([
        clientsApi.getById(clientId),
        projectsApi.getAll(),
        paymentsApi.getAll(),
        tasksApi.getAll()
      ])

      setClient(clientData)
      // Filter projects for this client
      const clientProjects = projectsData.filter(project => project.client_id === clientId)
      setProjects(clientProjects)

      // Filter payments for this client's projects
      const clientProjectIds = clientProjects.map(p => p.id)
      const clientPayments = paymentsData.filter(payment =>
        clientProjectIds.includes(payment.project_id)
      )
      setPayments(clientPayments)

      // Filter tasks for this client's projects
      const clientTasks = tasksData.filter(task =>
        task.project_id && clientProjectIds.includes(task.project_id)
      )
      setTasks(clientTasks)
    } catch (error: any) {
      toast.error('Failed to load client details')
      router.push('/clients')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (clientId) {
      fetchClientData()
    }
  }, [clientId])

  const handleEditSuccess = () => {
    setIsEditModalOpen(false)
    fetchClientData()
  }

  const handlePaymentSuccess = () => {
    setIsAddPaymentModalOpen(false)
    fetchClientData()
  }

  const handleAddProjectSuccess = () => {
    setIsAddProjectModalOpen(false)
    fetchClientData()
  }

  // Sort projects based on selected criteria
  const sortProjects = (projects: Project[]) => {
    const sorted = [...projects]

    switch (sortBy) {
      case 'new_to_old':
        return sorted.sort((a, b) => new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime())

      case 'pending_payment':
        return sorted.sort((a, b) => {
          const aPending = a.amount_pending || 0
          const bPending = b.amount_pending || 0
          return bPending - aPending // Highest pending first
        })

      case 'project_status':
        const statusOrder = { 'active': 1, 'in_progress': 2, 'on_hold': 3, 'completed': 4, 'cancelled': 5 }
        return sorted.sort((a, b) => {
          const aOrder = statusOrder[a.status as keyof typeof statusOrder] || 6
          const bOrder = statusOrder[b.status as keyof typeof statusOrder] || 6
          return aOrder - bOrder
        })

      default:
        return sorted
    }
  }

  // Quick action handlers
  const handleOpenProjectFolder = (project: Project) => {
    // Generate a project folder URL based on project ID or name
    const folderUrl = `${window.location.origin}/projects/${project.id}/files`
    window.open(folderUrl, '_blank')
    toast.success(`Opening project folder for ${project.name}`)
  }

  const handleShareFolderLink = async (project: Project) => {
    // Generate a shareable folder link
    const shareUrl = `${window.location.origin}/shared/projects/${project.id}/files`

    try {
      await navigator.clipboard.writeText(shareUrl)
      toast.success('Folder link copied to clipboard!')
    } catch (err) {
      // Fallback for browsers that don't support clipboard API
      const textArea = document.createElement('textarea')
      textArea.value = shareUrl
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      toast.success('Folder link copied to clipboard!')
    }
  }

  // Client quick action handlers
  const handleOpenClientFolder = () => {
    if (!client) return
    // Generate a client folder URL based on client ID
    const folderUrl = `${window.location.origin}/clients/${client.id}/files`
    window.open(folderUrl, '_blank')
    toast.success(`Opening client folder for ${client.name}`)
  }

  const handleShareClientLink = async () => {
    if (!client) return
    // Generate a shareable client folder link
    const shareUrl = `${window.location.origin}/shared/clients/${client.id}/files`

    try {
      await navigator.clipboard.writeText(shareUrl)
      toast.success('Client folder link copied to clipboard!')
    } catch (err) {
      // Fallback for browsers that don't support clipboard API
      const textArea = document.createElement('textarea')
      textArea.value = shareUrl
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      toast.success('Client folder link copied to clipboard!')
    }
  }

  const handleDeleteClient = () => {
    setIsDeleteModalOpen(true)
  }

  const confirmDeleteClient = async () => {
    if (!client) return
    try {
      await clientsApi.delete(client.id)
      toast.success('Client deleted successfully')
      router.push('/clients')
    } catch (error) {
      console.error('Error deleting client:', error)
      toast.error('Failed to delete client')
    } finally {
      setIsDeleteModalOpen(false)
    }
  }

  // Calculate financial summary
  const financialSummary = projects.reduce(
    (acc, project) => {
      acc.totalValue += project.total_amount || 0
      acc.totalReceived += project.amount_received || 0
      acc.totalPending += project.amount_pending || 0
      return acc
    },
    { totalValue: 0, totalReceived: 0, totalPending: 0 }
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!client) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-2">Client not found</h2>
          <p className="text-muted-foreground mb-4">The client you're looking for doesn't exist.</p>
          <Button onClick={() => router.push('/clients')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Clients
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="relative flex items-center justify-center">
        {/* Back Button - Absolute positioned to left */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push('/clients')}
          className="absolute left-0"
          title="Back to Clients"
        >
          <ArrowLeft className="w-4 h-4" />
        </Button>

        {/* Centered Title */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-foreground">{client.name}</h1>
          <p className="text-muted-foreground">Client Details</p>
        </div>

        {/* Actions Dropdown - Absolute positioned to right */}
        <div className="absolute right-0">
          <DropdownMenu
            trigger={
              <Button variant="outline" size="sm">
                <MoreVertical className="w-4 h-4" />
              </Button>
            }
            align="right"
          >
            <DropdownMenuItem onClick={handleOpenClientFolder}>
              <FolderOpen className="w-4 h-4 mr-2" />
              Open Client Folder
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleShareClientLink}>
              <Share2 className="w-4 h-4 mr-2" />
              Share Folder Link
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setIsEditModalOpen(true)}>
              <Edit className="w-4 h-4 mr-2" />
              Edit Client
            </DropdownMenuItem>
            <div className="border-t border-gray-200 dark:border-gray-700 my-1"></div>
            <DropdownMenuItem
              onClick={handleDeleteClient}
              className="text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Client
            </DropdownMenuItem>
          </DropdownMenu>
        </div>
      </div>

      {/* Client Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Basic Info Card */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Basic Information</h3>
          <div className="space-y-3">
            {client.email && (
              <div className="flex items-center text-sm">
                <Mail className="w-4 h-4 mr-3 text-muted-foreground" />
                <span className="text-foreground">{client.email}</span>
              </div>
            )}
            {client.phone && (
              <div className="flex items-center text-sm">
                <Phone className="w-4 h-4 mr-3 text-muted-foreground" />
                <span className="text-foreground">{client.phone}</span>
              </div>
            )}
            {client.address && (
              <div className="flex items-center text-sm">
                <MapPin className="w-4 h-4 mr-3 text-muted-foreground" />
                <span className="text-foreground">{client.address}</span>
              </div>
            )}
            {client.gst_number && (
              <div className="flex items-center text-sm">
                <FileText className="w-4 h-4 mr-3 text-muted-foreground" />
                <span className="text-foreground">GST: {client.gst_number}</span>
              </div>
            )}
          </div>
        </div>

        {/* Financial Summary Card */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Financial Summary</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm">
                <DollarSign className="w-4 h-4 mr-2 text-blue-600" />
                <span className="text-muted-foreground">Total Project Value</span>
              </div>
              <span className="font-semibold text-foreground">₹{financialSummary.totalValue.toLocaleString()}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm">
                <TrendingUp className="w-4 h-4 mr-2 text-green-600" />
                <span className="text-muted-foreground">Total Received</span>
              </div>
              <span className="font-semibold text-green-600">₹{financialSummary.totalReceived.toLocaleString()}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm">
                <Clock className="w-4 h-4 mr-2 text-orange-600" />
                <span className="text-muted-foreground">Total Pending</span>
              </div>
              <span className="font-semibold text-orange-600">₹{financialSummary.totalPending.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Project Stats Card */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Project Statistics</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Total Projects</span>
              <span className="font-semibold text-foreground">{projects.length}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Active Projects</span>
              <span className="font-semibold text-foreground">
                {projects.filter(p => p.status === 'active').length}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Completed Projects</span>
              <span className="font-semibold text-foreground">
                {projects.filter(p => p.status === 'completed').length}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Persons Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <ContactPersonsList
          clientId={clientId}
          projects={projects}
          onContactsChange={fetchClientData}
        />
      </div>

      {/* Projects Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <h3 className="text-lg font-semibold text-foreground">Projects</h3>
            <span className="text-sm text-muted-foreground">{projects.length} project{projects.length !== 1 ? 's' : ''}</span>
          </div>
          <div className="flex items-center gap-2">
            {/* Sort Dropdown */}
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'new_to_old' | 'pending_payment' | 'project_status')}
                className="flex h-8 items-center justify-center rounded-md border border-input bg-background px-3 py-1 text-xs ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 appearance-none pr-8"
              >
                <option value="new_to_old">New to Old</option>
                <option value="pending_payment">Pending Payment</option>
                <option value="project_status">Project Status</option>
              </select>
              <ArrowUpDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-muted-foreground pointer-events-none" />
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAddPaymentModalOpen(true)}
              disabled={projects.length === 0}
              className="text-xs"
            >
              <DollarSign className="w-4 h-4 mr-1" />
              Add Payment
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAddProjectModalOpen(true)}
              className="text-xs"
            >
              <Plus className="w-4 h-4 mr-1" />
              Add Project
            </Button>
          </div>
        </div>
        {projects.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No projects found for this client.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {sortProjects(projects).map((project) => {
              const paymentProgress = project.total_amount > 0
                ? (project.amount_received / project.total_amount) * 100
                : 0

              const projectTasks = tasks.filter(task => task.project_id === project.id)
              const completedTasks = projectTasks.filter(task => task.status === 'completed')

              return (
                <div
                  key={project.id}
                  className="group border border-border rounded-lg p-4 hover:bg-muted/30 transition-all duration-200 hover:border-blue-200 dark:hover:border-blue-800"
                >
                  <div className="flex items-center justify-between">
                    {/* Left side - Project info */}
                    <div
                      className="flex-1 min-w-0 mr-4 cursor-pointer"
                      onClick={() => router.push(`/projects/${project.id}`)}
                    >
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-semibold text-foreground group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate">
                          {project.name}
                        </h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          project.status === 'active' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' :
                          project.status === 'completed' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400' :
                          project.status === 'on_hold' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400' :
                          'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                        }`}>
                          {project.status.replace('_', ' ')}
                        </span>
                      </div>

                      {project.description && (
                        <p className="text-sm text-muted-foreground mb-3 line-clamp-1">
                          {project.description}
                        </p>
                      )}

                      {/* Compact progress indicators */}
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <span>Payment Status</span>
                          <div className="w-16 bg-muted rounded-full h-1.5">
                            <div
                              className={`h-1.5 rounded-full ${
                                paymentProgress === 100 ? 'bg-green-500' :
                                paymentProgress >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${Math.max(paymentProgress, 2)}%` }}
                            />
                          </div>
                          <span>{Math.round(paymentProgress)}%</span>
                        </div>

                        <div className="flex items-center gap-2">
                          <span>Project Tasks</span>
                          <span className="font-medium">
                            {completedTasks.length} / {projectTasks.length} completed
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Right side - Financial info and Quick Actions */}
                    <div className="flex items-start gap-4">
                      {/* Financial info */}
                      <div className="text-right">
                        <div className="text-lg font-bold text-foreground mb-1">
                          ₹{project.total_amount.toLocaleString()}
                        </div>
                        <div className="text-xs text-muted-foreground space-y-0.5">
                          <div className="flex items-center justify-end gap-2">
                            <span>Received:</span>
                            <span className="font-medium text-green-600 dark:text-green-400">
                              ₹{project.amount_received.toLocaleString()}
                            </span>
                          </div>
                          {project.amount_pending > 0 && (
                            <div className="flex items-center justify-end gap-2">
                              <span>Pending:</span>
                              <span className="font-medium text-orange-600 dark:text-orange-400">
                                ₹{project.amount_pending.toLocaleString()}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Quick Actions */}
                      <div className="flex flex-col gap-1.5 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleOpenProjectFolder(project)
                          }}
                          className="h-7 px-2 text-xs hover:bg-blue-50 hover:border-blue-200 dark:hover:bg-blue-950 dark:hover:border-blue-800"
                          title="Open Project Folder"
                        >
                          <FolderOpen className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleShareFolderLink(project)
                          }}
                          className="h-7 px-2 text-xs hover:bg-green-50 hover:border-green-200 dark:hover:bg-green-950 dark:hover:border-green-800"
                          title="Share Folder Link"
                        >
                          <Share2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Recent Payments Section */}
      {payments.length > 0 && (
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-foreground">Recent Payments</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAddPaymentModalOpen(true)}
              className="text-xs"
            >
              <Plus className="w-4 h-4 mr-1" />
              Add Payment
            </Button>
          </div>
          <div className="space-y-3">
            {payments
              .sort((a, b) => new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime())
              .slice(0, 5)
              .map((payment) => {
                const project = projects.find(p => p.id === payment.project_id)
                return (
                  <div key={payment.id} className="flex items-center justify-between border-b border-border pb-2">
                    <div>
                      <p className="font-medium text-foreground">₹{payment.amount.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">
                        {project?.name} • {new Date(payment.payment_date).toLocaleDateString()}
                      </p>
                      <p className="text-xs text-muted-foreground capitalize">
                        {payment.payment_method.replace('_', ' ')}
                        {payment.reference_number && ` • Ref: ${payment.reference_number}`}
                      </p>
                    </div>
                  </div>
                )
              })}
          </div>
        </div>
      )}

      {/* Notes Section */}
      {client.notes && (
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Notes</h3>
          <p className="text-muted-foreground whitespace-pre-wrap">{client.notes}</p>
        </div>
      )}

      {/* Edit Client Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Client"
        size="lg"
      >
        <ClientForm
          client={client}
          onSuccess={handleEditSuccess}
          onCancel={() => setIsEditModalOpen(false)}
        />
      </Modal>

      {/* Add Payment Modal */}
      <Modal
        isOpen={isAddPaymentModalOpen}
        onClose={() => setIsAddPaymentModalOpen(false)}
        title="Record Payment"
        size="lg"
      >
        <PaymentForm
          onSuccess={handlePaymentSuccess}
          onCancel={() => setIsAddPaymentModalOpen(false)}
          clientId={clientId}
        />
      </Modal>

      {/* Add Project Modal */}
      <Modal
        isOpen={isAddProjectModalOpen}
        onClose={() => setIsAddProjectModalOpen(false)}
        title="Add New Project"
        size="lg"
      >
        <ProjectForm
          onSuccess={handleAddProjectSuccess}
          onCancel={() => setIsAddProjectModalOpen(false)}
          preselectedClientId={clientId}
        />
      </Modal>

      {/* Delete Client Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Client"
        size="sm"
      >
        <div className="space-y-4">
          <div className="flex items-center gap-3 p-4 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800">
            <Trash2 className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-red-900 dark:text-red-100">
                Are you sure you want to delete this client?
              </h4>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                This will permanently delete <strong>{client.name}</strong> and all associated data. This action cannot be undone.
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={() => setIsDeleteModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="outline"
              onClick={confirmDeleteClient}
              className="bg-red-600 text-white border-red-600 hover:bg-red-700 hover:border-red-700"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Client
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}
