'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import {
  LayoutDashboard,
  Users,
  FolderOpen,
  Calendar,
  CalendarDays,
  DollarSign,
  CheckSquare,
  CreditCard,
  Map,
  Settings,
  LogOut,
  Menu,
  ChevronLeft,
  Building2
} from 'lucide-react'
import toast from 'react-hot-toast'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'Clients', href: '/clients', icon: Users },
  { name: 'Vendors', href: '/vendors', icon: Building2 },
  { name: 'Projects', href: '/projects', icon: FolderOpen },
  { name: 'Shoots', href: '/shoots', icon: Calendar },
  { name: 'Calendar', href: '/calendar', icon: CalendarDays },
  { name: 'Finances', href: '/finances', icon: DollarSign },
  { name: 'Tasks', href: '/tasks', icon: CheckSquare },
  { name: 'Map', href: '/map', icon: Map },
  { name: 'Settings', href: '/settings', icon: Settings },
]

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [isExpanded, setIsExpanded] = useState(false)

  // Load sidebar state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('sidebar-expanded')
    if (savedState !== null) {
      setIsExpanded(JSON.parse(savedState))
    }
  }, [])

  // Save sidebar state to localStorage when it changes
  const toggleSidebar = () => {
    const newState = !isExpanded
    setIsExpanded(newState)
    localStorage.setItem('sidebar-expanded', JSON.stringify(newState))
  }

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('Signed out successfully')
      router.push('/login')
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign out')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" suppressHydrationWarning={true}>
        <div className="text-center" suppressHydrationWarning={true}>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" suppressHydrationWarning={true}></div>
          <p className="mt-4 text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800" suppressHydrationWarning={true}>
      {/* Sidebar */}
      <div className={`fixed left-0 top-0 ${isExpanded ? 'w-64' : 'w-16'} bg-gradient-to-b from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-xl shadow-2xl border-r border-gray-200 dark:border-gray-700 transition-all duration-300 flex flex-col h-screen z-30 group/sidebar`}>
        {/* Header */}
        <div className={`${isExpanded ? 'p-6' : 'p-4'} border-b border-gray-200 dark:border-gray-700 flex-shrink-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10`}>
          <div className="flex items-center justify-between">
            {isExpanded ? (
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Cymatics</h1>
                <p className="text-sm text-muted-foreground">Drone Service Management</p>
              </div>
            ) : (
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">C</span>
              </div>
            )}
          </div>
        </div>

        {/* Arrow Toggle Button */}
        <button
          onClick={toggleSidebar}
          className={`absolute top-4 ${isExpanded ? '-right-1.5' : '-right-1.5'}
            w-3 h-6 sm:w-3.5 sm:h-7 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-r-sm shadow-lg
            hover:bg-gray-50 dark:hover:bg-gray-700 hover:shadow-xl transition-all duration-300 z-40
            flex items-center justify-center group
            opacity-0 group-hover/sidebar:opacity-100 translate-x-2 group-hover/sidebar:translate-x-0`}
          aria-label={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
        >
          <ChevronLeft
            className={`w-2.5 h-2.5 sm:w-3 sm:h-3 text-gray-600 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-transform duration-200 ${
              isExpanded ? '' : 'rotate-180'
            }`}
          />
        </button>

        {/* Navigation - Flexible area */}
        <nav className="mt-6 flex-1 overflow-y-auto">
          <div className={`${isExpanded ? 'px-3' : 'px-2'}`}>
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center ${isExpanded ? 'px-4 py-3' : 'px-2 py-3 justify-center'} text-sm font-medium rounded-xl mb-2 transition-all duration-200 group ${
                    isActive
                      ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-700 dark:text-purple-300 shadow-lg border border-purple-500/30'
                      : 'text-muted-foreground hover:bg-gradient-to-r hover:from-muted/50 hover:to-muted/30 hover:text-foreground hover:shadow-md'
                  }`}
                  title={!isExpanded ? item.name : undefined}
                >
                  <item.icon className={`w-5 h-5 ${isExpanded ? 'mr-3' : ''} ${isActive ? 'text-purple-600 dark:text-purple-400' : 'group-hover:scale-110'} transition-all duration-200`} />
                  {isExpanded && (
                    <span className={`${isActive ? 'font-semibold' : ''} transition-all duration-200`}>
                      {item.name}
                    </span>
                  )}
                  {isActive && isExpanded && (
                    <div className="ml-auto w-2 h-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full"></div>
                  )}
                </Link>
              )
            })}
          </div>
        </nav>

        {/* User Info - Fixed at bottom */}
        <div className={`${isExpanded ? 'p-4' : 'p-2'} border-t border-gray-200 dark:border-gray-700 flex-shrink-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5`}>
          {isExpanded ? (
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-sm">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold text-card-foreground truncate">{user.name}</p>
                <p className="text-xs text-muted-foreground capitalize">{user.role}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSignOut}
                className="p-2 hover:bg-red-50 dark:hover:bg-red-900/20 hover:border-red-300 dark:hover:border-red-700 hover:text-red-600 dark:hover:text-red-400 transition-all duration-200"
              >
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          ) : (
            <div className="flex flex-col items-center space-y-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-sm">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSignOut}
                className="p-2 hover:bg-red-50 dark:hover:bg-red-900/20 hover:border-red-300 dark:hover:border-red-700 hover:text-red-600 dark:hover:text-red-400 transition-all duration-200"
                title="Sign Out"
              >
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className={`${isExpanded ? 'ml-64' : 'ml-16'} transition-all duration-300 min-h-screen bg-gradient-to-br from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-700`}>
        <main className="p-8 relative">
          {/* Subtle background pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-pink-500/5 pointer-events-none"></div>
          <div className="relative z-10">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
