'use client'

import { useState, useEffect, useCallback } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'

import { Modal } from '@/components/ui/modal'
import { VendorForm } from '@/components/forms/VendorForm'
import { supabase } from '@/lib/supabase'
import {
  ArrowLeft,
  Edit,
  Mail,
  Phone,
  MapPin,
  User,
  Briefcase,
  Calendar,
  DollarSign,
  TrendingUp,
  BarChart3,
  Eye,
  EyeOff,
  Building2,
  CreditCard,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react'
import Link from 'next/link'
import toast from 'react-hot-toast'
import { InteractiveSpendingChart } from '@/components/ui/interactive-spending-chart'
import type { OutsourcingVendor, Shoot, Project } from '@/types'

interface VendorProject extends Shoot {
  project?: Project & {
    payments?: Array<{
      id: string
      amount: number
      payment_date: string
      payment_method: string
      reference_number?: string
      notes?: string
    }>
  }
}

interface VendorPayment {
  id: string
  project_id: string
  project_name: string
  client_name: string
  shoot_date: string
  outsourcing_cost: number
  payment_status: 'pending' | 'paid' | 'overdue' | 'cancelled'
  payment_amount?: number
  payment_due_date?: string
  payment_date?: string
  payment_notes?: string
}

interface VendorStats {
  totalProjects: number
  totalSpent: number
  averageCostPerProject: number
  activeProjects: number
  completedProjects: number
  lastProjectDate: string | null
  monthlySpending: { month: string; amount: number }[]
  costByStatus: { status: string; amount: number; count: number }[]
  paymentStats: {
    totalPaid: number
    totalPending: number
    paidProjects: number
    pendingProjects: number
  }
}

export default function VendorDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const vendorId = params.id as string

  const [vendor, setVendor] = useState<OutsourcingVendor | null>(null)
  const [projects, setProjects] = useState<VendorProject[]>([])
  const [vendorPayments, setVendorPayments] = useState<VendorPayment[]>([])
  const [stats, setStats] = useState<VendorStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)



  // Fetch vendor details
  const fetchVendorDetails = useCallback(async () => {
    try {
      setLoading(true)

      // Fetch vendor info
      const { data: vendorData, error: vendorError } = await supabase
        .from('outsourcing_vendors')
        .select('*')
        .eq('id', vendorId)
        .single()

      if (vendorError) {
        throw vendorError
      }
      setVendor(vendorData)

      // Fetch vendor's projects with payment information using API endpoint
      const response = await fetch(`/api/vendor-shoots?vendorId=${vendorId}`)
      const apiData = await response.json()

      if (!response.ok) {
        throw new Error(apiData.error || 'Failed to fetch shoots data')
      }

      const shootsData = apiData.shoots || []
      setProjects(shootsData)

      // Process vendor payment information
      const payments: VendorPayment[] = shootsData.map((shoot: any) => ({
        id: shoot.id,
        project_id: shoot.project_id,
        project_name: shoot.project?.name || 'Unknown Project',
        client_name: shoot.project?.client?.name || 'Unknown Client',
        shoot_date: shoot.scheduled_date,
        outsourcing_cost: shoot.outsourcing_cost || 0,
        payment_status: shoot.project?.vendor_payment_status || 'pending',
        payment_amount: shoot.project?.vendor_payment_amount,
        payment_due_date: shoot.project?.vendor_payment_due_date,
        payment_date: shoot.project?.vendor_payment_date,
        payment_notes: shoot.project?.vendor_payment_notes,
      }))
      setVendorPayments(payments)

      // Calculate stats
      const totalProjects = shootsData?.length || 0
      const totalSpent = shootsData?.reduce((sum: number, shoot: any) => {
        const cost = shoot.outsourcing_cost || 0
        return sum + cost
      }, 0) || 0
      const averageCostPerProject = totalProjects > 0 ? totalSpent / totalProjects : 0
      const activeProjects = shootsData?.filter((p: any) => p.status === 'scheduled').length || 0
      const completedProjects = shootsData?.filter((p: any) => p.status === 'completed').length || 0
      const lastProjectDate = shootsData?.[0]?.scheduled_date || null

      // Calculate monthly spending
      const monthlySpending = shootsData?.reduce((acc: any[], shoot: any) => {
        if (shoot.outsourcing_cost && shoot.scheduled_date) {
          const month = new Date(shoot.scheduled_date).toLocaleDateString('en-IN', { year: 'numeric', month: 'short' })
          const existing = acc.find((item: { month: string; amount: number }) => item.month === month)
          const cost = shoot.outsourcing_cost || 0
          if (existing) {
            existing.amount += cost
          } else {
            acc.push({ month, amount: cost })
          }
        }
        return acc
      }, [] as { month: string; amount: number }[]) || []

      // Calculate cost by status
      const costByStatus = shootsData?.reduce((acc: any[], shoot: any) => {
        if (shoot.outsourcing_cost && shoot.status) {
          const existing = acc.find((item: { status: string; amount: number; count: number }) => item.status === shoot.status)
          const cost = shoot.outsourcing_cost || 0
          if (existing) {
            existing.amount += cost
            existing.count += 1
          } else {
            acc.push({ status: shoot.status, amount: cost, count: 1 })
          }
        }
        return acc
      }, [] as { status: string; amount: number; count: number }[]) || []

      // Calculate payment stats based on actual vendor payment status
      const paymentStats = {
        totalPaid: 0,
        totalPending: 0,
        paidProjects: 0,
        pendingProjects: 0
      }

      // Use actual vendor payment status from project data
      vendorPayments.forEach(payment => {
        const cost = payment.outsourcing_cost || 0
        const paymentAmount = payment.payment_amount || cost

        switch (payment.payment_status) {
          case 'paid':
            paymentStats.totalPaid += paymentAmount
            paymentStats.paidProjects += 1
            break
          case 'pending':
          case 'overdue': // Treat overdue as pending
          default:
            paymentStats.totalPending += cost
            paymentStats.pendingProjects += 1
            break
        }
      })

      setStats({
        totalProjects,
        totalSpent,
        averageCostPerProject,
        activeProjects,
        completedProjects,
        lastProjectDate,
        monthlySpending: monthlySpending.sort((a: { month: string; amount: number }, b: { month: string; amount: number }) => new Date(a.month).getTime() - new Date(b.month).getTime()),
        costByStatus,
        paymentStats
      })

    } catch (error) {
      toast.error('Failed to load vendor details')
    } finally {
      setLoading(false)
    }
  }, [vendorId])

  useEffect(() => {
    if (vendorId) {
      fetchVendorDetails()
    }
  }, [vendorId, fetchVendorDetails])

  const handleEditSuccess = () => {
    setIsEditModalOpen(false)
    fetchVendorDetails()
  }

  const toggleVendorStatus = async () => {
    if (!vendor) return

    try {
      const { error } = await supabase
        .from('outsourcing_vendors')
        .update({ 
          is_active: !vendor.is_active, 
          updated_at: new Date().toISOString() 
        })
        .eq('id', vendor.id)
      
      if (error) throw error
      toast.success(`Vendor ${vendor.is_active ? 'deactivated' : 'activated'} successfully`)
      fetchVendorDetails()
    } catch (error: any) {
      toast.error(error.message || 'Failed to update vendor status')
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading vendor details...</p>
        </div>
      </div>
    )
  }

  if (!vendor) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">Vendor not found</h3>
          <p className="text-muted-foreground mb-6">The vendor you're looking for doesn't exist.</p>
          <Link href="/vendors">
            <Button variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Vendors
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="flex flex-col gap-4">
        {/* Back Button */}
        <div className="flex justify-start">
          <Link href="/vendors">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </Link>
        </div>

        {/* Centered Vendor Info */}
        <div className="flex flex-col items-center text-center gap-4">
          {/* Vendor Avatar */}
          <div
            className={
              `w-20 h-20 bg-gradient-to-br rounded-xl flex items-center justify-center flex-shrink-0 ${
                vendor.is_active ? 'from-purple-500 to-purple-600' : 'from-gray-400 to-gray-500'
              }`
            }
          >
            <span className="text-white font-semibold text-3xl">
              {vendor.name.charAt(0).toUpperCase()}
            </span>
          </div>

          {/* Vendor Info */}
          <div className="flex flex-col items-center">
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-3xl sm:text-4xl font-semibold text-foreground">{vendor.name}</h1>
              <span
                className={
                  `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    vendor.is_active
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                  }`
                }
              >
                {vendor.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
            {vendor.specialization && (
              <div className="flex items-center gap-2 text-muted-foreground mb-2">
                <Briefcase className="w-4 h-4" />
                <span>{vendor.specialization}</span>
              </div>
            )}

            {/* Contact Info */}
            <div className="flex flex-wrap items-center justify-center gap-4 text-sm text-muted-foreground mb-2">
              {vendor.contact_person && (
                <div className="flex items-center gap-1">
                  <User className="w-4 h-4" />
                  <span>{vendor.contact_person}</span>
                </div>
              )}
              {vendor.email && (
                <div className="flex items-center gap-1">
                  <Mail className="w-4 h-4" />
                  <span>{vendor.email}</span>
                </div>
              )}
              {vendor.phone && (
                <div className="flex items-center gap-1">
                  <Phone className="w-4 h-4" />
                  <span>{vendor.phone}</span>
                </div>
              )}
            </div>

            <p className="text-sm text-muted-foreground">
              Member since {formatDate(vendor.created_at)}
            </p>

            {/* Action Buttons */}
            <div className="flex gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={toggleVendorStatus}
                className="flex items-center gap-2"
              >
                {vendor.is_active ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                {vendor.is_active ? 'Deactivate' : 'Activate'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditModalOpen(true)}
                className="flex items-center gap-2"
              >
                <Edit className="w-4 h-4" />
                Edit
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Projects</p>
                <p className="text-2xl font-bold text-foreground">{stats.totalProjects}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Spent</p>
                <p className="text-2xl font-bold text-foreground">{formatCurrency(stats.totalSpent)}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Cost/Project</p>
                <p className="text-2xl font-bold text-foreground">{formatCurrency(stats.averageCostPerProject)}</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Projects</p>
                <p className="text-2xl font-bold text-foreground">{stats.activeProjects}</p>
              </div>
              <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <Calendar className="w-6 h-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Payment Analytics */}
      {stats && (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4">Payment Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-green-50 dark:bg-green-900/10 rounded-lg p-4 border border-green-200 dark:border-green-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-700 dark:text-green-400">Paid</p>
                  <p className="text-xl font-bold text-green-800 dark:text-green-300">{formatCurrency(stats.paymentStats.totalPaid)}</p>
                  <p className="text-xs text-green-600 dark:text-green-500">{stats.paymentStats.paidProjects} projects</p>
                </div>
                <div className="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/10 rounded-lg p-4 border border-yellow-200 dark:border-yellow-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-yellow-700 dark:text-yellow-400">Pending</p>
                  <p className="text-xl font-bold text-yellow-800 dark:text-yellow-300">{formatCurrency(stats.paymentStats.totalPending)}</p>
                  <p className="text-xs text-yellow-600 dark:text-yellow-500">{stats.paymentStats.pendingProjects} projects</p>
                </div>
                <div className="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Contact Information */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-lg font-semibold text-foreground mb-4">Contact Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {vendor.email && (
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <Mail className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Email</p>
                <p className="text-foreground">{vendor.email}</p>
              </div>
            </div>
          )}

          {vendor.phone && (
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <Phone className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Phone</p>
                <p className="text-foreground">{vendor.phone}</p>
              </div>
            </div>
          )}

          {vendor.contact_person && (
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <User className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Contact Person</p>
                <p className="text-foreground">{vendor.contact_person}</p>
              </div>
            </div>
          )}

          {vendor.address && (
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                <MapPin className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Address</p>
                <p className="text-foreground">{vendor.address}</p>
              </div>
            </div>
          )}
        </div>

        {vendor.notes && (
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <p className="text-sm font-medium text-muted-foreground mb-2">Notes</p>
            <p className="text-foreground">{vendor.notes}</p>
          </div>
        )}
      </div>



      {/* Project History */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-foreground">Project History</h2>
          <span className="text-sm text-muted-foreground">
            {projects.length} project{projects.length !== 1 ? 's' : ''}
          </span>
        </div>

        {projects.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">No outsourced projects found</h3>
            <p className="text-muted-foreground">
              This vendor hasn't been assigned to any outsourced shoots yet, or the shoots don't have outsourcing costs set.
            </p>
            <div className="mt-4 text-sm text-muted-foreground">
              <p>To link this vendor to projects:</p>
              <ol className="list-decimal list-inside mt-2 space-y-1">
                <li>Create or edit a shoot in a project</li>
                <li>Check "This is an outsourced shoot"</li>
                <li>Select this vendor and set the outsourcing cost</li>
              </ol>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {projects.map((project) => (
              <div key={project.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-medium text-foreground">
                        {project.project?.name || 'Untitled Project'}
                      </h3>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        project.status === 'completed'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : project.status === 'scheduled'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                      }`}>
                        {project.status?.replace('_', ' ') || 'Unknown'}
                      </span>

                      {project.project?.vendor_payment_status && (
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                          project.project.vendor_payment_status === 'paid'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            : project.project.vendor_payment_status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                            : project.project.vendor_payment_status === 'overdue'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                        }`}>
                          {project.project.vendor_payment_status === 'pending' ? 'Pending Payment' :
                           project.project.vendor_payment_status === 'paid' ? 'Paid' :
                           project.project.vendor_payment_status === 'overdue' ? 'Overdue' :
                           project.project.vendor_payment_status}
                        </span>
                      )}
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(project.scheduled_date)}</span>
                      </div>

                      {project.location && (
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4" />
                          <span className="truncate">{project.location}</span>
                        </div>
                      )}

                      {project.outsourcing_cost && (
                        <div className="flex items-center gap-2">
                          <DollarSign className="w-4 h-4" />
                          <span className="font-medium text-foreground">
                            {formatCurrency(project.outsourcing_cost)}
                          </span>
                        </div>
                      )}
                    </div>

                    {project.notes && (
                      <p className="mt-2 text-sm text-muted-foreground line-clamp-2">
                        {project.notes}
                      </p>
                    )}
                  </div>

                  <Link href={`/projects/${project.project?.id}`} className="ml-4">
                    <Button variant="outline" size="sm">
                      View Project
                    </Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Financial Analytics */}
      {stats && stats.totalSpent > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Interactive Monthly Spending Chart */}
          <InteractiveSpendingChart
            data={stats.monthlySpending}
            title="Monthly Spending Trend"
          />

          {/* Cost by Project Status */}
          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="text-lg font-semibold text-foreground mb-4">Cost by Project Status</h2>
            {stats.costByStatus.length > 0 ? (
              <div className="space-y-4">
                {stats.costByStatus.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        item.status === 'completed'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : item.status === 'in_progress'
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                          : item.status === 'scheduled'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                      }`}>
                        {item.status.replace('_', ' ')}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {item.count} project{item.count !== 1 ? 's' : ''}
                      </span>
                    </div>
                    <span className="text-sm font-semibold text-foreground">
                      {formatCurrency(item.amount)}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground text-center py-8">No cost data available</p>
            )}
          </div>
        </div>
      )}

      {/* Financial Summary */}
      {stats && stats.totalSpent > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4">Financial Summary</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <DollarSign className="w-8 h-8 text-green-600 dark:text-green-400" />
              </div>
              <p className="text-2xl font-bold text-foreground">{formatCurrency(stats.totalSpent)}</p>
              <p className="text-sm text-muted-foreground">Total Investment</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
              <p className="text-2xl font-bold text-foreground">{formatCurrency(stats.averageCostPerProject)}</p>
              <p className="text-sm text-muted-foreground">Average per Project</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <BarChart3 className="w-8 h-8 text-purple-600 dark:text-purple-400" />
              </div>
              <p className="text-2xl font-bold text-foreground">
                {stats.completedProjects > 0 ? formatCurrency(stats.totalSpent / stats.completedProjects) : '₹0'}
              </p>
              <p className="text-sm text-muted-foreground">Cost per Completed Project</p>
            </div>
          </div>
        </div>
      )}

      {/* Payment History */}
      {vendorPayments.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-lg font-semibold text-foreground mb-6 flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Payment History
          </h2>
          <div>
            <div className="space-y-4">
              {vendorPayments.map((payment) => (
                <div
                  key={payment.id}
                  className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-medium text-foreground">{payment.project_name}</h3>
                      <span
                        className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-semibold ${
                          payment.payment_status === 'paid'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            : payment.payment_status === 'overdue'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                            : payment.payment_status === 'cancelled'
                            ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                        }`}
                      >
                        {payment.payment_status === 'paid' && <CheckCircle className="w-3 h-3" />}
                        {payment.payment_status === 'pending' && <Clock className="w-3 h-3" />}
                        {payment.payment_status === 'overdue' && <AlertCircle className="w-3 h-3" />}
                        {payment.payment_status === 'cancelled' && <XCircle className="w-3 h-3" />}
                        {payment.payment_status.charAt(0).toUpperCase() + payment.payment_status.slice(1)}
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p>Client: {payment.client_name}</p>
                      <p>Shoot Date: {new Date(payment.shoot_date).toLocaleDateString()}</p>
                      {payment.payment_due_date && (
                        <p>Due Date: {new Date(payment.payment_due_date).toLocaleDateString()}</p>
                      )}
                      {payment.payment_date && (
                        <p>Paid Date: {new Date(payment.payment_date).toLocaleDateString()}</p>
                      )}
                      {payment.payment_notes && (
                        <p>Notes: {payment.payment_notes}</p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-foreground">
                      {formatCurrency(payment.outsourcing_cost)}
                    </div>
                    {payment.payment_amount && payment.payment_amount !== payment.outsourcing_cost && (
                      <div className="text-sm text-muted-foreground">
                        Paid: {formatCurrency(payment.payment_amount)}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Payment Summary */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-lg font-semibold text-foreground">
                    {vendorPayments.filter(p => p.payment_status === 'paid').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Paid</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-foreground">
                    {vendorPayments.filter(p => p.payment_status === 'pending').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Pending</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-foreground">
                    {vendorPayments.filter(p => p.payment_status === 'overdue').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Overdue</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-foreground">
                    {formatCurrency(
                      vendorPayments
                        .filter(p => p.payment_status === 'paid')
                        .reduce((sum, p) => sum + (p.payment_amount || p.outsourcing_cost), 0)
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Paid</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Vendor Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Vendor"
      >
        <VendorForm
          vendor={vendor}
          onSuccess={handleEditSuccess}
          onCancel={() => setIsEditModalOpen(false)}
        />
      </Modal>
    </div>
  )
}
