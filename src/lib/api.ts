import { createClientSupabaseClient } from './auth'
import { getUpdatedProjectAmounts } from './project-calculations'
import type {
  User,
  UserRole,
  Client,
  ContactPerson,
  Project,
  Shoot,
  Expense,
  Task,
  Payment,
  Notification,
  ActivityLog,
  OutsourcingVendor,
  CreateClientForm,
  CreateContactPersonForm,
  CreateProjectForm,
  CreateShootForm,
  CreateExpenseForm,
  CreateTaskForm,
  DashboardStats,
  PaginatedResponse
} from '@/types'

const supabase = createClientSupabaseClient()

// Users API
export const usersApi = {
  async getAll(): Promise<User[]> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .order('name')

    if (error) throw error
    return data || []
  },

  async getByRole(role: UserRole): Promise<User[]> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('role', role)
      .order('name')

    if (error) throw error
    return data || []
  },

  async getUsersByRoles(): Promise<Record<string, User[]>> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .in('role', ['pilot', 'editor', 'manager', 'admin'])
      .order('name')

    if (error) throw error

    const usersByRole: Record<string, User[]> = {
      pilot: [],
      editor: [],
      accounts: [], // manager and admin can handle accounts tasks
    }

    data?.forEach(user => {
      if (user.role === 'pilot') {
        usersByRole.pilot.push(user)
      } else if (user.role === 'editor') {
        usersByRole.editor.push(user)
      } else if (user.role === 'manager' || user.role === 'admin') {
        usersByRole.accounts.push(user)
      }
    })

    return usersByRole
  }
}

// Clients API
export const clientsApi = {
  async getAll(): Promise<Client[]> {
    const { data, error } = await supabase
      .from('clients')
      .select(`
        *,
        contact_persons (*)
      `)
      .order('name')

    if (error) throw error
    return data || []
  },

  async getById(id: string): Promise<Client | null> {
    const { data, error } = await supabase
      .from('clients')
      .select(`
        *,
        contact_persons (*)
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  async create(client: CreateClientForm): Promise<Client> {
    const { data, error } = await supabase
      .from('clients')
      .insert(client)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, updates: Partial<CreateClientForm>): Promise<Client> {
    const { data, error } = await supabase
      .from('clients')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Contact Persons API
export const contactPersonsApi = {
  async getByClientId(clientId: string): Promise<ContactPerson[]> {
    const { data, error } = await supabase
      .from('contact_persons')
      .select('*')
      .eq('client_id', clientId)
      .order('is_primary', { ascending: false })
      .order('name')

    if (error) throw error
    return data || []
  },

  async create(contactPerson: CreateContactPersonForm & { client_id: string }): Promise<ContactPerson> {
    const { data, error } = await supabase
      .from('contact_persons')
      .insert(contactPerson)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, contactPerson: Partial<CreateContactPersonForm>): Promise<ContactPerson> {
    const { data, error } = await supabase
      .from('contact_persons')
      .update(contactPerson)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('contact_persons')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  async setPrimary(id: string, clientId: string): Promise<void> {
    // First, unset all primary contacts for this client
    await supabase
      .from('contact_persons')
      .update({ is_primary: false })
      .eq('client_id', clientId)

    // Then set the specified contact as primary
    const { error } = await supabase
      .from('contact_persons')
      .update({ is_primary: true })
      .eq('id', id)

    if (error) throw error
  }
}

// Projects API
export const projectsApi = {
  async getAll(): Promise<Project[]> {
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        client:clients(*),
        contact_person:contact_persons(*),
        shoots:shoots(count)
      `)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  async getById(id: string): Promise<Project | null> {
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        client:clients(*),
        contact_person:contact_persons(*),
        shoots:shoots(*),
        payments:payments(*),
        tasks:tasks(*)
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  async create(project: CreateProjectForm): Promise<Project> {
    const { data, error } = await supabase
      .from('projects')
      .insert({
        ...project,
        total_amount: 0, // Will be calculated from shoots
        gst_inclusive: false, // Will be determined by client GST status
        amount_received: 0,
        amount_pending: 0
      })
      .select(`
        *,
        client:clients(*),
        contact_person:contact_persons(*)
      `)
      .single()

    if (error) throw error

    // Auto-generate default tasks based on client type
    if (data.client?.client_type) {
      try {
        await this.createDefaultTasks(data.id, data.client.client_type)
      } catch (taskError) {
        console.error('Failed to create default tasks:', taskError)
        // Don't fail the project creation if task creation fails
      }
    }

    return data
  },

  async createDefaultTasks(projectId: string, clientType: string, shootDate?: string): Promise<void> {
    console.log('createDefaultTasks called with:', { projectId, clientType, shootDate })
    const { getDefaultTasksForClientType, createTasksFromTemplates } = await import('@/lib/default-tasks')

    // Get default task templates for this client type
    const templates = getDefaultTasksForClientType(clientType)
    console.log('Templates found:', templates)
    if (templates.length === 0) {
      console.log('No templates found for client type:', clientType)
      return
    }

    // Get users by role for task assignment
    const usersByRole = await usersApi.getUsersByRoles()
    console.log('Users by role:', usersByRole)

    // Create a mapping of role to first available user ID
    const roleToUserId: Record<string, string> = {}
    if (usersByRole.pilot.length > 0) roleToUserId.pilot = usersByRole.pilot[0].id
    if (usersByRole.editor.length > 0) roleToUserId.editor = usersByRole.editor[0].id
    if (usersByRole.accounts.length > 0) roleToUserId.accounts = usersByRole.accounts[0].id

    // Fallback: if no specific role users exist, use admin/manager for all tasks
    const fallbackUserId = usersByRole.accounts.length > 0 ? usersByRole.accounts[0].id : ''
    if (!roleToUserId.pilot && fallbackUserId) roleToUserId.pilot = fallbackUserId
    if (!roleToUserId.editor && fallbackUserId) roleToUserId.editor = fallbackUserId
    if (!roleToUserId.accounts && fallbackUserId) roleToUserId.accounts = fallbackUserId

    console.log('Role to user mapping:', roleToUserId)

    // If we still don't have any users, we can't create tasks
    if (!fallbackUserId) {
      console.log('No users available to assign tasks to')
      return
    }

    // Create task forms from templates with shoot date for due date calculation
    const taskForms = createTasksFromTemplates(templates, projectId, roleToUserId, shootDate)
    console.log('Task forms created:', taskForms)

    // Create tasks in batch - only create tasks that have assigned users
    for (const taskForm of taskForms) {
      if (taskForm.assigned_to) {
        console.log('Creating task:', taskForm)
        try {
          const createdTask = await tasksApi.create(taskForm)
          console.log('Task created successfully:', createdTask.id)
        } catch (taskCreateError) {
          console.error('Failed to create individual task:', taskCreateError)
          throw taskCreateError
        }
      } else {
        console.log('Skipping task without assigned user:', taskForm.title)
      }
    }
    console.log('All tasks created successfully')
  },

  async update(id: string, updates: Partial<CreateProjectForm>): Promise<Project> {
    const { data, error } = await supabase
      .from('projects')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        client:clients(*),
        contact_person:contact_persons(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  async recalculateProjectTotal(projectId: string): Promise<Project> {
    // Get project with client and shoots
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        *,
        client:clients(*),
        shoots:shoots(*)
      `)
      .eq('id', projectId)
      .single()

    if (projectError) throw projectError
    if (!project) throw new Error('Project not found')

    // Calculate new amounts
    const updatedAmounts = getUpdatedProjectAmounts(
      project.shoots || [],
      project.client,
      project.amount_received
    )

    // Update project with new amounts
    const { data: updatedProject, error: updateError } = await supabase
      .from('projects')
      .update(updatedAmounts)
      .eq('id', projectId)
      .select(`
        *,
        client:clients(*),
        contact_person:contact_persons(*)
      `)
      .single()

    if (updateError) throw updateError
    return updatedProject
  }
}

// Shoots API
export const shootsApi = {
  async getAll(): Promise<Shoot[]> {
    const { data, error } = await supabase
      .from('shoots')
      .select(`
        *,
        project:projects(*),
        pilot:users(*)
      `)
      .order('scheduled_date')

    if (error) throw error
    return data || []
  },

  async getById(id: string): Promise<Shoot | null> {
    const { data, error } = await supabase
      .from('shoots')
      .select(`
        *,
        project:projects(
          *,
          client:clients(*)
        ),
        pilot:users(*),
        vendor:outsourcing_vendors(*)
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  async getUpcoming(): Promise<Shoot[]> {
    const { data, error } = await supabase
      .from('shoots')
      .select(`
        *,
        project:projects(*),
        pilot:users(*)
      `)
      .gte('scheduled_date', new Date().toISOString())
      .eq('status', 'scheduled')
      .order('scheduled_date')
      .limit(10)

    if (error) throw error
    return data || []
  },

  async create(shoot: CreateShootForm): Promise<Shoot> {
    const { data, error } = await supabase
      .from('shoots')
      .insert(shoot)
      .select(`
        *,
        project:projects(
          *,
          client:clients(*)
        ),
        pilot:users(*)
      `)
      .single()

    if (error) throw error

    // Recalculate project total
    await projectsApi.recalculateProjectTotal(shoot.project_id)

    // Create default tasks with due dates based on shoot date
    if (data.project?.client?.client_type) {
      try {
        await projectsApi.createDefaultTasks(
          data.project_id,
          data.project.client.client_type,
          data.scheduled_date
        )
      } catch (taskError) {
        console.error('Failed to create default tasks for shoot:', taskError)
        // Don't fail the shoot creation if task creation fails
      }
    }

    return data
  },

  async update(id: string, updates: Partial<CreateShootForm>): Promise<Shoot> {
    const { data, error } = await supabase
      .from('shoots')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        project:projects(*),
        pilot:users(*)
      `)
      .single()

    if (error) throw error

    // Recalculate project total
    await projectsApi.recalculateProjectTotal(data.project_id)

    return data
  },

  async delete(id: string): Promise<void> {
    // Get the shoot to find the project_id before deleting
    const { data: shoot, error: getError } = await supabase
      .from('shoots')
      .select('project_id')
      .eq('id', id)
      .single()

    if (getError) throw getError

    const { error } = await supabase
      .from('shoots')
      .delete()
      .eq('id', id)

    if (error) throw error

    // Recalculate project total
    if (shoot) {
      await projectsApi.recalculateProjectTotal(shoot.project_id)
    }
  }
}

// Expenses API
export const expensesApi = {
  async getAll(): Promise<Expense[]> {
    const { data, error } = await supabase
      .from('expenses')
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .order('date', { ascending: false })

    if (error) throw error
    return data || []
  },

  async getByProjectId(projectId: string): Promise<Expense[]> {
    const { data, error } = await supabase
      .from('expenses')
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .eq('project_id', projectId)
      .order('date', { ascending: false })

    if (error) throw error
    return data || []
  },

  async create(expense: CreateExpenseForm): Promise<Expense> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    const { data, error } = await supabase
      .from('expenses')
      .insert({
        ...expense,
        user_id: user.id
      })
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, updates: Partial<CreateExpenseForm>): Promise<Expense> {
    const { data, error } = await supabase
      .from('expenses')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('expenses')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Tasks API
export const tasksApi = {
  async getAll(): Promise<Task[]> {
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:shoots(*)
      `)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  async getMyTasks(): Promise<Task[]> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:shoots(*)
      `)
      .eq('assigned_to', user.id)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  async create(task: CreateTaskForm): Promise<Task> {
    const { data, error } = await supabase
      .from('tasks')
      .insert(task)
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:shoots(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, updates: Partial<CreateTaskForm>): Promise<Task> {
    const { data, error } = await supabase
      .from('tasks')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:shoots(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Vendors API
export const vendorsApi = {
  async getAll(): Promise<OutsourcingVendor[]> {
    const { data, error } = await supabase
      .from('outsourcing_vendors')
      .select('*')
      .order('name')

    if (error) throw error
    return data || []
  },

  async getActive(): Promise<OutsourcingVendor[]> {
    const { data, error } = await supabase
      .from('outsourcing_vendors')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data || []
  },

  async getById(id: string): Promise<OutsourcingVendor | null> {
    const { data, error } = await supabase
      .from('outsourcing_vendors')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  async create(vendor: Omit<OutsourcingVendor, 'id' | 'created_at' | 'updated_at'>): Promise<OutsourcingVendor> {
    const { data, error } = await supabase
      .from('outsourcing_vendors')
      .insert(vendor)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, updates: Partial<Omit<OutsourcingVendor, 'id' | 'created_at' | 'updated_at'>>): Promise<OutsourcingVendor> {
    const { data, error } = await supabase
      .from('outsourcing_vendors')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('outsourcing_vendors')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Payments API
export const paymentsApi = {
  async getAll(): Promise<Payment[]> {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        project:projects(*)
      `)
      .order('payment_date', { ascending: false })

    if (error) throw error
    return data || []
  },

  async create(payment: Omit<Payment, 'id' | 'created_at' | 'updated_at'>): Promise<Payment> {
    const { data, error } = await supabase
      .from('payments')
      .insert(payment)
      .select(`
        *,
        project:projects(*)
      `)
      .single()

    if (error) throw error

    // Update project's amount_received and recalculate totals
    await paymentsApi.updateProjectAmountReceived(payment.project_id)

    return data
  },

  async updateProjectAmountReceived(projectId: string): Promise<void> {
    // Get all payments for this project
    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .select('amount')
      .eq('project_id', projectId)

    if (paymentsError) throw paymentsError

    // Calculate total amount received
    const totalReceived = payments?.reduce((sum, p) => sum + p.amount, 0) || 0

    // Get project with client and shoots to recalculate totals
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        *,
        client:clients(*),
        shoots:shoots(*)
      `)
      .eq('id', projectId)
      .single()

    if (projectError) throw projectError
    if (!project) throw new Error('Project not found')

    // Calculate updated amounts with new amount_received
    const updatedAmounts = getUpdatedProjectAmounts(
      project.shoots || [],
      project.client,
      totalReceived
    )

    // Update project with new amounts
    const { error: updateError } = await supabase
      .from('projects')
      .update(updatedAmounts)
      .eq('id', projectId)

    if (updateError) throw updateError
  },

  async update(id: string, updates: Partial<Omit<Payment, 'id' | 'created_at' | 'updated_at'>>): Promise<Payment> {
    // Get the payment to find the project_id before updating
    const { data: existingPayment, error: getError } = await supabase
      .from('payments')
      .select('project_id')
      .eq('id', id)
      .single()

    if (getError) throw getError

    const { data, error } = await supabase
      .from('payments')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        project:projects(*)
      `)
      .single()

    if (error) throw error

    // Update project's amount_received and recalculate totals
    await paymentsApi.updateProjectAmountReceived(existingPayment.project_id)

    // If project_id changed, also update the new project
    if (updates.project_id && updates.project_id !== existingPayment.project_id) {
      await paymentsApi.updateProjectAmountReceived(updates.project_id)
    }

    return data
  },

  async delete(id: string): Promise<void> {
    // Get the payment to find the project_id before deleting
    const { data: payment, error: getError } = await supabase
      .from('payments')
      .select('project_id')
      .eq('id', id)
      .single()

    if (getError) throw getError

    const { error } = await supabase
      .from('payments')
      .delete()
      .eq('id', id)

    if (error) throw error

    // Update project's amount_received and recalculate totals
    if (payment) {
      await paymentsApi.updateProjectAmountReceived(payment.project_id)
    }
  }
}

// Dashboard API
export const dashboardApi = {
  async getStats(): Promise<DashboardStats> {
    // Get all stats in parallel
    const [
      projectsResult,
      shootsResult,
      tasksResult,
      paymentsResult,
      expensesResult
    ] = await Promise.all([
      supabase.from('projects').select('status, total_amount, vendor_payment_status, vendor_payment_amount, vendor_payment_due_date'),
      supabase.from('shoots').select('status, scheduled_date, is_outsourced, outsourcing_cost'),
      supabase.from('tasks').select('status, due_date'),
      supabase.from('payments').select('amount, payment_date'),
      supabase.from('expenses').select('category, amount')
    ])

    if (projectsResult.error) throw projectsResult.error
    if (shootsResult.error) throw shootsResult.error
    if (tasksResult.error) throw tasksResult.error
    if (paymentsResult.error) throw paymentsResult.error
    if (expensesResult.error) throw expensesResult.error

    const projects = projectsResult.data || []
    const shoots = shootsResult.data || []
    const tasks = tasksResult.data || []
    const payments = paymentsResult.data || []
    const expenses = expensesResult.data || []

    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)

    // Calculate vendor payment statistics
    const totalOutsourcingCosts = shoots
      .filter(s => s.is_outsourced)
      .reduce((sum, s) => sum + (s.outsourcing_cost || 0), 0)

    const totalOutsourcingExpenses = expenses
      .filter(e => e.category === 'outsourcing')
      .reduce((sum, e) => sum + e.amount, 0)

    const pendingVendorPayments = Math.max(0, totalOutsourcingCosts - totalOutsourcingExpenses)

    // Count projects with overdue vendor payments
    const overdueVendorProjects = projects.filter(p =>
      p.vendor_payment_status === 'overdue' ||
      (p.vendor_payment_due_date && new Date(p.vendor_payment_due_date) < now && p.vendor_payment_status === 'pending')
    ).length

    const pendingVendorProjects = projects.filter(p =>
      p.vendor_payment_status === 'pending' && p.vendor_payment_amount && p.vendor_payment_amount > 0
    ).length

    return {
      totalProjects: projects.length,
      activeProjects: projects.filter(p => p.status === 'active').length,
      completedShoots: shoots.filter(s => s.status === 'completed').length,
      pendingPayments: projects.filter(p => p.status === 'active').length, // Simplified
      totalRevenue: payments.reduce((sum, p) => sum + p.amount, 0),
      monthlyRevenue: payments
        .filter(p => new Date(p.payment_date) >= startOfMonth)
        .reduce((sum, p) => sum + p.amount, 0),
      upcomingShoots: shoots.filter(s =>
        s.status === 'scheduled' && new Date(s.scheduled_date) >= now
      ).length,
      overdueTasks: tasks.filter(t =>
        t.status !== 'completed' &&
        t.due_date &&
        new Date(t.due_date) < now
      ).length,
      vendorPayments: {
        totalPending: pendingVendorPayments,
        totalOverdue: 0, // Would need more complex calculation based on due dates
        pendingCount: pendingVendorProjects,
        overdueCount: overdueVendorProjects
      }
    }
  }
}
