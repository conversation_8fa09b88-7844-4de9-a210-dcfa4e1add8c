import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Browser client for SSR
export function createSupabaseBrowserClient() {
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Database types
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          role: 'admin' | 'manager' | 'pilot' | 'editor'
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name: string
          role?: 'admin' | 'manager' | 'pilot' | 'editor'
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          role?: 'admin' | 'manager' | 'pilot' | 'editor'
          avatar_url?: string | null
          updated_at?: string
        }
      }
      clients: {
        Row: {
          id: string
          name: string
          email: string | null
          phone: string | null
          address: string | null
          gst_number: string | null
          has_gst: boolean
          client_type: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email?: string | null
          phone?: string | null
          address?: string | null
          gst_number?: string | null
          has_gst?: boolean
          client_type?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string | null
          phone?: string | null
          address?: string | null
          gst_number?: string | null
          has_gst?: boolean
          client_type?: string | null
          notes?: string | null
          updated_at?: string
        }
      }
      contact_persons: {
        Row: {
          id: string
          client_id: string
          name: string
          phone: string | null
          email: string | null
          designation: string | null
          is_primary: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          client_id: string
          name: string
          phone?: string | null
          email?: string | null
          designation?: string | null
          is_primary?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          client_id?: string
          name?: string
          phone?: string | null
          email?: string | null
          designation?: string | null
          is_primary?: boolean
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          name: string
          description: string | null
          client_id: string
          contact_person_id: string | null
          location: string | null
          google_maps_link: string | null
          status: 'active' | 'completed' | 'on_hold' | 'cancelled'
          total_amount: number
          gst_inclusive: boolean
          amount_received: number
          amount_pending: number
          vendor_payment_status: 'pending' | 'paid' | 'overdue' | 'cancelled' | null
          vendor_payment_amount: number | null
          vendor_payment_due_date: string | null
          vendor_payment_date: string | null
          vendor_payment_notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          client_id: string
          contact_person_id?: string | null
          location?: string | null
          google_maps_link?: string | null
          status?: 'active' | 'completed' | 'on_hold' | 'cancelled'
          total_amount: number
          gst_inclusive?: boolean
          amount_received?: number
          amount_pending?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          client_id?: string
          contact_person_id?: string | null
          location?: string | null
          google_maps_link?: string | null
          status?: 'active' | 'completed' | 'on_hold' | 'cancelled'
          total_amount?: number
          gst_inclusive?: boolean
          amount_received?: number
          amount_pending?: number
          updated_at?: string
        }
      }
      shoots: {
        Row: {
          id: string
          project_id: string
          scheduled_date: string
          scheduled_end_date: string | null
          actual_date: string | null
          status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled'
          pilot_id: string | null
          amount: number
          location: string | null
          google_maps_link: string | null
          notes: string | null
          weather_conditions: string | null
          onedrive_folder_path: string | null
          is_recurring: boolean
          recurring_pattern: 'weekly' | 'monthly' | 'quarterly' | null
          is_outsourced: boolean | null
          vendor_id: string | null
          outsourcing_cost: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          scheduled_date: string
          scheduled_end_date?: string | null
          actual_date?: string | null
          status?: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled'
          pilot_id?: string | null
          amount?: number
          location?: string | null
          google_maps_link?: string | null
          notes?: string | null
          weather_conditions?: string | null
          onedrive_folder_path?: string | null
          is_recurring?: boolean
          recurring_pattern?: 'weekly' | 'monthly' | 'quarterly' | null
          is_outsourced?: boolean | null
          vendor_id?: string | null
          outsourcing_cost?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          scheduled_date?: string
          scheduled_end_date?: string | null
          actual_date?: string | null
          status?: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled'
          pilot_id?: string | null
          amount?: number
          location?: string | null
          google_maps_link?: string | null
          notes?: string | null
          weather_conditions?: string | null
          onedrive_folder_path?: string | null
          is_recurring?: boolean
          recurring_pattern?: 'weekly' | 'monthly' | 'quarterly' | null
          is_outsourced?: boolean | null
          vendor_id?: string | null
          outsourcing_cost?: number | null
          updated_at?: string
        }
      }
      expenses: {
        Row: {
          id: string
          description: string
          amount: number
          category: 'cymatics' | 'salary' | 'gadgets' | 'outsourcing' | 'asset' | 'loan_repayment' | 'investments' | 'fuel_travel' | 'food_snacks' | 'others' | 'entertainment' | 'gopi' | 'yaso' | 'adithyan'
          date: string
          project_id: string | null
          user_id: string
          receipt_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          description: string
          amount: number
          category: 'cymatics' | 'salary' | 'gadgets' | 'outsourcing' | 'asset' | 'loan_repayment' | 'investments' | 'fuel_travel' | 'food_snacks' | 'others' | 'entertainment' | 'gopi' | 'yaso' | 'adithyan'
          date: string
          project_id?: string | null
          user_id: string
          receipt_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          description?: string
          amount?: number
          category?: 'cymatics' | 'salary' | 'gadgets' | 'outsourcing' | 'asset' | 'loan_repayment' | 'investments' | 'fuel_travel' | 'food_snacks' | 'others' | 'entertainment' | 'gopi' | 'yaso' | 'adithyan'
          date?: string
          project_id?: string | null
          user_id?: string
          receipt_url?: string | null
          updated_at?: string
        }
      }
      tasks: {
        Row: {
          id: string
          title: string
          description: string | null
          status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
          priority: 'low' | 'medium' | 'high' | 'urgent'
          assigned_to: string
          project_id: string | null
          shoot_id: string | null
          due_date: string | null
          completed_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
          priority?: 'low' | 'medium' | 'high' | 'urgent'
          assigned_to: string
          project_id?: string | null
          shoot_id?: string | null
          due_date?: string | null
          completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
          priority?: 'low' | 'medium' | 'high' | 'urgent'
          assigned_to?: string
          project_id?: string | null
          shoot_id?: string | null
          due_date?: string | null
          completed_at?: string | null
          updated_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          project_id: string
          amount: number
          payment_date: string
          payment_method: 'cash' | 'bank_transfer' | 'credit_card' | 'cheque' | 'other'
          reference_number: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          amount: number
          payment_date: string
          payment_method: 'cash' | 'bank_transfer' | 'credit_card' | 'cheque' | 'other'
          reference_number?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          amount?: number
          payment_date?: string
          payment_method?: 'cash' | 'bank_transfer' | 'credit_card' | 'cheque' | 'other'
          reference_number?: string | null
          notes?: string | null
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          title: string
          message: string
          type: 'shoot_reminder' | 'payment_overdue' | 'task_deadline' | 'general'
          read: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          message: string
          type: 'shoot_reminder' | 'payment_overdue' | 'task_deadline' | 'general'
          read?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          message?: string
          type?: 'shoot_reminder' | 'payment_overdue' | 'task_deadline' | 'general'
          read?: boolean
        }
      }
      activity_logs: {
        Row: {
          id: string
          user_id: string
          action: string
          entity_type: 'project' | 'shoot' | 'client' | 'expense' | 'task' | 'payment'
          entity_id: string
          details: any | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          action: string
          entity_type: 'project' | 'shoot' | 'client' | 'expense' | 'task' | 'payment'
          entity_id: string
          details?: any | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          action?: string
          entity_type?: 'project' | 'shoot' | 'client' | 'expense' | 'task' | 'payment'
          entity_id?: string
          details?: any | null
        }
      }
      outsourcing_vendors: {
        Row: {
          id: string
          name: string
          email: string | null
          phone: string | null
          address: string | null
          contact_person: string | null
          specialization: string | null
          notes: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email?: string | null
          phone?: string | null
          address?: string | null
          contact_person?: string | null
          specialization?: string | null
          notes?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string | null
          phone?: string | null
          address?: string | null
          contact_person?: string | null
          specialization?: string | null
          notes?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
